import base64

def is_valid_base64(encoded_string):
    try:
        # 尝试解码字符串
        decoded_bytes = base64.b64decode(encoded_string, validate=True)
        # 再次尝试将解码后的字节重新编码为Base64，以确保原始字符串确实是一个有效的Base64编码
        re_encoded_string = base64.b64encode(decoded_bytes).decode('utf-8')
        # 检查重新编码后的字符串与原始字符串是否相等
        return re_encoded_string == encoded_string
    except (base64.binascii.Error, UnicodeDecodeError, ValueError):
        # 如果出现异常，则不是有效的Base64编码
        return False

def get_base64_type(base64_str):
    '''
    判断base64是哪种类型的文件
    '''
    base64_str = base64.b64decode(base64_str)
    TYPE_JPG = "img"
    TYPE_GIF = "img"
    TYPE_PNG = "img"
    TYPE_BMP = "img"
    TYPE_doc = "doc"
    TYPE_docx = "docx"
    TYPE_pdf="pdf"
    TYPE_UNKNOWN = "unknown"
    #读取文件的前几个字节来判断图片格式
    _type = base64_str.hex().upper()
    if _type.startswith("FFD8FF"):
        return TYPE_JPG
    elif _type.startswith("89504E47"):
        return TYPE_PNG
    elif _type.startswith("47494638"):
        return TYPE_GIF
    elif _type.startswith("424D"):
        return TYPE_BMP
    elif _type.startswith("D0CF11E0"):
        return TYPE_doc
    elif _type.startswith("504B0304"):
        return TYPE_docx
    if _type.startswith("255044462D312E"):
        return TYPE_pdf
    else:
        return TYPE_UNKNOWN
    
def file2base64(file_path):
    with open(file_path, "rb") as f:
        base64_str = base64.b64encode(f.read()).decode('utf-8')
        return base64_str

def base642file(base64_str, file_path):
    with open(file_path, "wb") as f:
        f.write(base64.b64decode(base64_str))

def get_base64_size(base64_str):
    """
    获取base64图片的大小
    """
    base64_str = base64.b64decode(base64_str)
    return len(base64_str)