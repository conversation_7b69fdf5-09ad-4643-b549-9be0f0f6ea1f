# -*- coding: utf-8 -*-
# @Time    : 2024/05/21 17:39
# <AUTHOR> Chen 
# @File    : 成本准确率.py
# @Software: PyCharm
# @Desc    :
import pandas as pd

# 读取Excel文件
file_name = r"D:\myproj\2025\caiwuyu\成本结算表.xlsx"  # Excel文件名
sheet1_name = 'Sheet1'  # 第一个工作表名
sheet2_name = 'Sheet2'  # 第二个工作表名

# 加载两个工作表
df1 = pd.read_excel(file_name, sheet_name=sheet1_name)
df2 = pd.read_excel(file_name, sheet_name=sheet2_name)

# 比较两个工作表的每一列数据相同的数量
comparison_results = {}
for col in df1.columns:
    # 比较相同列的数据
    same_values = df1[col] == df2[col]
    # 计算相同数据的数量
    num_same_values = same_values.sum()
    comparison_results[col] = num_same_values

# 打印结果
for col, num_same in comparison_results.items():
    print(f"Column '{col}' has {num_same} same values.")
