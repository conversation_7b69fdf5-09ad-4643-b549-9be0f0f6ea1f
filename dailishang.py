from yolo_engine import YoloEngine
from ocr_engine import OcrEngine
from flask import Response
import json
import os
import base64
from tools import is_valid_base64, get_base64_size, get_base64_type
import logging
import copy
import cv2
import numpy as np
import time
from datetime import datetime
import traceback
import re
from document_converter import DocumentConverter
current_file_path = os.path.abspath(__file__)
now_dir = os.path.dirname(current_file_path)
# 创建一个logger
logger = logging.getLogger()
logger.setLevel(logging.DEBUG)  # 设置日志级别

# 创建一个handler，用于写入日志文件
file_handler = logging.FileHandler(os.path.join(now_dir,'app.log'), mode='w', encoding='utf-8')
# file_handler = logging.FileHandler('/home/<USER>/app.log', mode='w', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)  # 设置handler的日志级别

# 创建一个formatter，用于设置日志格式
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
file_handler.setFormatter(formatter)

# 将handler添加到logger
logger.addHandler(file_handler)


class DaiLiShang():
    def __init__(self):
        self.label = ['agent_signature','agent_date','agent_seal','agent_handprint','refundMoney','refundName','refundAccount','bank','account','company_signature','company_date','company_seal','company_handprint','company_comment1','company_comment2','company_comment3','other_recycle_signature','other_recycle_date','other_recycle_seal','other_recycle_handprint','other_recycle_comment1','other_recycle_comment2','other_fine_signature','other_fine_date','other_fine_seal','other_fine_handprint','other_fine_comment1','other_fine_comment2','director_signature','director_date','director_seal','director_handprint','director_comment1','director_comment2','director_comment3','manage_signature','manage_date','manage_seal','manage_handprint','manage_comment1','manage_comment2','manage_comment3']
        self.yolo = YoloEngine(yolo_weight_path='./weights/dailishang0529.pt', label=self.label, conf=0.5)
        self.ocr = OcrEngine()
        self.base_api_res = {
            'res':'',
            'code':0,
            'msg':''
        }
        self.goal1 = ['refundMoney','refundName','refundAccount','bank','account']
        self.doc_converter = DocumentConverter()

    def check_post_data(self, post_map):
        api_res = copy.deepcopy(self.base_api_res)
        try:
            file_base64 = post_map.get("imgBase64", "")
        except Exception as e:
            logger.warning("缺失参数：", e)
            api_res['code'] = 30001
            api_res['msg'] = '缺少参数'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')

        if not is_valid_base64(file_base64):
            logger.warning("参数无效")
            api_res['code'] = 30002
            api_res['msg'] = '文件无效，请输入有效的base64编码'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')

        fsize = get_base64_size(file_base64)
        if fsize < 1024 * 50 or fsize > 1024 * 1024 * 30:
            logger.warning("图片太小，请上传大于50kB，小于30MB的图片")
            api_res['code'] = 30003
            api_res['msg'] = '图片太小，请上传大于50kB，小于30MB的图片'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')

        ftype = get_base64_type(file_base64)
        if ftype not in ["docx", "pdf", "img"]:
            logger.warning("不支持的图片格式，请上传jpg/jpeg/png/bmp格式的图片或pdf、docx文件")
            api_res['code'] = 30004
            api_res['msg'] = '不支持的图片格式，请上传jpg/jpeg/png/bmp格式的图片或pdf、docx文件'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        try:
            # 根据文件类型处理
            if ftype == "img":
                # 原有的图片处理逻辑
                image_data = base64.b64decode(file_base64)
                image_array = np.frombuffer(image_data, dtype=np.uint8)
                cv_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                
                if cv_image is None:
                    logger.warning("图片解码失败")
                    api_res['code'] = 30005
                    api_res['msg'] = '图片解码失败，请检查图片格式'
                    return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
                    
            elif ftype in ["pdf", "docx"]:
                # 新增的文档转图片功能
                logger.info(f"检测到 {ftype} 文件，开始转换为图片...")
                
                # 初始化文档转换器（如果还没有的话）
                if not hasattr(self, 'doc_converter'):
                    self.doc_converter = DocumentConverter()
                
                try:
                    # 转换文档为图片
                    cv_image = self.doc_converter.convert_document_to_image(file_base64, ftype)
                    logger.info(f"{ftype} 文件转换成功")
                    
                except Exception as convert_error:
                    logger.error(f"{ftype} 转图片失败: {str(convert_error)}")
                    api_res['code'] = 30006
                    api_res['msg'] = f'{ftype.upper()} 文件转换失败: {str(convert_error)}'
                    return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            else:
                # 理论上不会到达这里，但为了安全起见
                logger.warning(f"未知文件类型: {ftype}")
                api_res['code'] = 30007
                api_res['msg'] = f'未知文件类型: {ftype}'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            # 验证最终图片
            if cv_image is None or cv_image.size == 0:
                logger.warning("处理后的图片为空")
                api_res['code'] = 30008
                api_res['msg'] = '处理后的图片为空，请检查文件内容'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            # 检查图片尺寸是否合理
            height, width = cv_image.shape[:2]
            if height < 100 or width < 100:
                logger.warning(f"图片尺寸过小: {width}x{height}")
                api_res['code'] = 30009
                api_res['msg'] = f'图片尺寸过小: {width}x{height}，请提供更大的图片'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            logger.info(f"文件处理成功，最终图片尺寸: {width}x{height}")
            return True, cv_image
            
        except Exception as e:
            logger.error(f"文件处理过程中发生错误: {str(e)}")
            api_res['code'] = 30010
            api_res['msg'] = f'文件处理失败: {str(e)}'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')



        # image_data = base64.b64decode(file_base64)
        # # 将字节流转换为numpy数组
        # image_array = np.frombuffer(image_data, dtype=np.uint8)
        # # 使用OpenCV将numpy数组解码为图像
        # cv_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
        # return True, cv_image

    def image_to_base64(self, image: np.ndarray, format: str = 'jpeg') -> str:
        # 检查图像是否是3通道的，并转换为RGB格式（如果是jpeg）
        if format == 'jpeg':
            if len(image.shape) == 3 and image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        # 根据指定格式进行编码
        if format.lower() == 'jpeg':
            success, buffer = cv2.imencode('.jpg', image)
        elif format.lower() == 'png':
            success, buffer = cv2.imencode('.png', image)
        else:
            raise ValueError("不支持的图像格式: 支持的格式有 'jpeg' 和 'png'")

        if not success:
            raise RuntimeError("图像编码失败")
        # 转换为base64字符串
        base64_str = base64.b64encode(buffer).decode('utf-8')
        mime_type = 'image/jpeg' if format == 'jpeg' else 'image/png'
        return f"data:{mime_type};base64,{base64_str}".split(",")[-1]

    def res_postprocess(self, res_dict):
        result = [
            {
                "key":"代理商填写",
                "value":
                {
                    "signature": res_dict['agent_signature'] if 'agent_signature' in res_dict else 0,
                    "date": res_dict['agent_date'] if 'agent_date' in res_dict else 0,
                    "seal": res_dict['agent_seal'] if 'agent_seal' in res_dict else 0,
                    "handprint": res_dict['agent_handprint'] if 'agent_handprint' in res_dict else 0,
                    "refundMoney": res_dict['refundMoney'] if 'refundMoney' in res_dict else "",
                    "refundName": res_dict['refundName'] if 'refundName' in res_dict else "",
                    "refundAccount": res_dict['refundAccount'] if 'refundAccount' in res_dict else "",
                    "bank": res_dict['bank'] if 'bank' in res_dict else "",
                    "account": res_dict['account'] if 'account' in res_dict else "",
                }
            },
            {
                "key":"移动公司填写",
                "value":
                {
                    "comment":"同意", # 需要根据检测结果绑定不同的意见
                    "signature": res_dict['company_signature'] if 'company_signature' in res_dict else 0,
                    "date": res_dict['company_date'] if 'company_date' in res_dict else 0,
                    "seal": res_dict['company_seal'] if 'company_seal' in res_dict else 0,
                    "handprint": res_dict['company_handprint'] if 'company_handprint' in res_dict else 0
                }
            },
            {
                "key":"其他事项确认-回收",
                "value":
                {
                    "comment":"是", # 需要根据检测结果绑定不同的意见
                    "signature": res_dict['other_recycle_signature'] if 'other_recycle_signature' in res_dict else 0,
                    "date": res_dict['other_recycle_date'] if 'other_recycle_date' in res_dict else 0,
                    "seal": res_dict['other_recycle_seal'] if 'other_recycle_seal' in res_dict else 0,
                    "handprint": res_dict['other_recycle_handprint'] if 'other_recycle_handprint' in res_dict else 0
                }
            },
            {
                "key":"其他事项确认-扣罚",
                "value":
                {
                    "comment":"是", # 需要根据检测结果绑定不同的意见
                    "signature": res_dict['other_fine_signature'] if 'other_fine_signature' in res_dict else 0,
                    "date": res_dict['other_fine_date'] if 'other_fine_date' in res_dict else 0,
                    "seal": res_dict['other_fine_seal'] if 'other_fine_seal' in res_dict else 0,
                    "handprint": res_dict['other_fine_handprint'] if 'other_fine_handprint' in res_dict else 0
                }
            },
            {
                "key":"网格审核",
                "value":
                {
                    "comment":"同意", # 需要根据检测结果绑定不同的意见
                    "signature": res_dict['director_signature'] if 'director_signature' in res_dict else 0,
                    "date": res_dict['director_date'] if 'director_date' in res_dict else 0,
                    "seal": res_dict['director_seal'] if 'director_seal' in res_dict else 0,
                    "handprint": res_dict['director_handprint'] if 'director_handprint' in res_dict else 0
                }
            },
            {
                "key":"部门经理审核",
                "value":
                {
                    "comment":"同意",
                    "signature": res_dict['manage_signature'] if 'manage_signature' in res_dict else 0,
                    "date": res_dict['manage_date'] if 'manage_date' in res_dict else 0,
                    "seal": res_dict['manage_seal'] if 'manage_seal' in res_dict else 0,
                    "handprint": res_dict['manage_handprint'] if 'manage_handprint' in res_dict else 0
                }
            }
        ]
        # 开始对特殊字段处理
        refundMoney = result[0]['value']['refundMoney'][:-1].replace('o','0').replace('O','0').replace('z','2').replace('Z','2').replace('s','5').replace('S','5').replace('l','1').replace('I','1').replace('_','').replace('-','')
        refundMoney = re.sub(r'[^0-9.,百千万零一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]', '', refundMoney)
        result[0]['value']['refundMoney'] = refundMoney

        refundName = result[0]['value']['refundName'].replace('_','').replace('-','').replace(":","").replace("：","")
        result[0]['value']['refundName'] = refundName

        refundAccount = result[0]['value']['refundAccount'].replace('_','').replace('-','').replace(":","").replace("：","")
        result[0]['value']['refundAccount'] = refundAccount

        bank = result[0]['value']['bank'].replace('_','').replace('-','').replace(":","").replace("：","")
        result[0]['value']['bank'] = bank

        account = result[0]['value']['account'].replace('_','').replace('-','').replace(":","").replace("：","")
        result[0]['value']['account'] = account

        if 'company_comment2' in res_dict:
            result[1]['value']['comment'] = '不同意'
        if 'company_comment1' in res_dict or 'company_comment3' in res_dict:
            result[1]['value']['comment'] = '同意'

        if 'other_recycle_comment2' in res_dict:
            result[2]['value']['comment'] = '否'
        if 'other_recycle_comment1' in res_dict:
            result[2]['value']['comment'] = '是'

        if 'other_recycle_comment2' in res_dict:
            result[3]['value']['comment'] = '否'
        if 'other_recycle_comment1' in res_dict:
            result[3]['value']['comment'] = '是'

        if 'director_comment2' in res_dict:
            result[4]['value']['comment'] = '不同意'
        if  'director_comment1' in res_dict or 'director_comment3' in res_dict:
            result[4]['value']['comment'] = '同意'

        if 'manage_comment2' in res_dict:
            result[5]['value']['comment'] = '不同意'
        if  'manage_comment1' in res_dict or 'manage_comment3' in res_dict:
            result[5]['value']['comment'] = '同意'
        return result

    def infer_res(self, img):
        yolo_res = self.yolo.infer_base(img)
        res_dict = {}
        for k,v in yolo_res.items():
            if k in self.goal1:
                for item in v:
                    data = self.image_to_base64(item)
                    ocr_res = self.ocr.ocr_base(data)
                    res_dict[k] = ocr_res
                    print(k, ocr_res)
            else:
                res_dict[k] = len(v)
        res = self.res_postprocess(res_dict)
        return res

    def infer_flow(self, post_map):
        try:
            t0 = time.time()
            tip1, img_cv = self.check_post_data(post_map)
            if not tip1:
                return img_cv
            ocr_res = self.infer_res(img_cv)
            api_res = {
                "result": {
                    "isSuc": True,
                    "code": 0,
                    "msg": str(time.time() - t0)[:5] + 's',
                    "res": ocr_res
                },
                "msg":"success","code":0
            }
            logger.info(api_res)
            return Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        except Exception as e:
            traceback.print_exc(e)
            logger.error(e)
            fail_res = {
                "res": None,
                "code": 30000,
                "msg": "普通识别错误"
            }
            return Response(json.dumps(fail_res, ensure_ascii=False), mimetype='application/json')

if __name__ == '__main__':
    dailishang = DaiLiShang()
    img = cv2.imdecode(np.fromfile(r"D:\myproj\2025\caiwuyu\acc_img\dailishang\302122C13230817029-0004.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)
    print(dailishang.infer_res(img))