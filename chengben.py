from yolo_engine import YoloEngine
from ocr_engine import OcrEngine
from flask import Response
import json
import os
import base64
from tools import is_valid_base64, get_base64_size, get_base64_type
import logging
import copy
import cv2
import numpy as np
import time
from datetime import datetime
import traceback
import re
from document_converter import DocumentConverter

current_file_path = os.path.abspath(__file__)
now_dir = os.path.dirname(current_file_path)
# 创建一个logger
logger = logging.getLogger()
logger.setLevel(logging.DEBUG)  # 设置日志级别

# 创建一个handler，用于写入日志文件
# file_handler = logging.FileHandler(os.path.join(now_dir,'app.log'), mode='w', encoding='utf-8')
file_handler = logging.FileHandler('/home/<USER>/app.log', mode='w', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)  # 设置handler的日志级别

# 创建一个formatter，用于设置日志格式
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
file_handler.setFormatter(formatter)

# 将handler添加到logger
logger.addHandler(file_handler)




class ChengBen():
    def __init__(self):
        self.label = ['contractNo','contractAmount','completPercentage','cumulativeAmount','paymentProportion','assessmentScore','assessmentPeriod','assessmentDeduction','settlementAmount_noTax','settlementAmount_Tax','cumulativePaidAmount','settlementType','totalSettlementAmount_noTax','totalSettlementAmount_Tax','seal','signature']
        self.yolo = YoloEngine(yolo_weight_path='./weights/chengben0603.pt', label=self.label)
        self.ocr = OcrEngine()
        self.base_api_res = {
            'res':'',
            'code':0,
            'msg':''
        }
        # 直接文字识别
        self.goal1 = ['contractAmount','completPercentage','cumulativeAmount','paymentProportion','assessmentScore','assessmentDeduction','settlementAmount_noTax','settlementAmount_Tax','cumulativePaidAmount','totalSettlementAmount_noTax','totalSettlementAmount_Tax']
        # 文字检测后识别
        self.goal2 = ['contractNo','assessmentPeriod','settlementType']
        self.doc_converter = DocumentConverter()


    def check_post_data(self, post_map):
        api_res = copy.deepcopy(self.base_api_res)
        try:
            file_base64 = post_map.get("base64_strs", "")
        except Exception as e:
            logger.warning("缺失参数：", e)
            api_res['code'] = 30001
            api_res['msg'] = '缺少参数'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        if not is_valid_base64(file_base64):
            logger.warning("参数无效")
            api_res['code'] = 30002
            api_res['msg'] = '文件无效，请输入有效的base64编码'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        fsize = get_base64_size(file_base64)
        if fsize < 1024 * 50 or fsize > 1024 * 1024 * 30:
            logger.warning("图片太小，请上传大于50kB，小于30MB的图片")
            api_res['code'] = 30003
            api_res['msg'] = '图片太小，请上传大于50kB，小于30MB的图片'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        ftype = get_base64_type(file_base64)
        if ftype not in ["docx", "pdf", "img"]:
            logger.warning("不支持的图片格式，请上传jpg/jpeg/png/bmp格式的图片或pdf、docx文件")
            api_res['code'] = 30004
            api_res['msg'] = '不支持的图片格式，请上传jpg/jpeg/png/bmp格式的图片或pdf、docx文件'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        try:
            # 根据文件类型处理
            if ftype == "img":
                # 原有的图片处理逻辑
                image_data = base64.b64decode(file_base64)
                image_array = np.frombuffer(image_data, dtype=np.uint8)
                cv_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                
                if cv_image is None:
                    logger.warning("图片解码失败")
                    api_res['code'] = 30005
                    api_res['msg'] = '图片解码失败，请检查图片格式'
                    return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
                    
            elif ftype in ["pdf", "docx"]:
                # 新增的文档转图片功能
                logger.info(f"检测到 {ftype} 文件，开始转换为图片...")
                
                # 初始化文档转换器（如果还没有的话）
                if not hasattr(self, 'doc_converter'):
                    self.doc_converter = DocumentConverter()
                
                try:
                    # 转换文档为图片
                    cv_image = self.doc_converter.convert_document_to_image(file_base64, ftype)
                    logger.info(f"{ftype} 文件转换成功")
                    
                except Exception as convert_error:
                    logger.error(f"{ftype} 转图片失败: {str(convert_error)}")
                    api_res['code'] = 30006
                    api_res['msg'] = f'{ftype.upper()} 文件转换失败: {str(convert_error)}'
                    return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            else:
                # 理论上不会到达这里，但为了安全起见
                logger.warning(f"未知文件类型: {ftype}")
                api_res['code'] = 30007
                api_res['msg'] = f'未知文件类型: {ftype}'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            # 验证最终图片
            if cv_image is None or cv_image.size == 0:
                logger.warning("处理后的图片为空")
                api_res['code'] = 30008
                api_res['msg'] = '处理后的图片为空，请检查文件内容'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            # 检查图片尺寸是否合理
            height, width = cv_image.shape[:2]
            if height < 100 or width < 100:
                logger.warning(f"图片尺寸过小: {width}x{height}")
                api_res['code'] = 30009
                api_res['msg'] = f'图片尺寸过小: {width}x{height}，请提供更大的图片'
                return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
            
            logger.info(f"文件处理成功，最终图片尺寸: {width}x{height}")
            return True, cv_image
            
        except Exception as e:
            logger.error(f"文件处理过程中发生错误: {str(e)}")
            api_res['code'] = 30010
            api_res['msg'] = f'文件处理失败: {str(e)}'
            return False, Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        
        

        # image_data = base64.b64decode(file_base64)
        # # 将字节流转换为numpy数组
        # image_array = np.frombuffer(image_data, dtype=np.uint8)
        # # 使用OpenCV将numpy数组解码为图像
        # cv_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
        # return True, cv_image
    def image_to_base64(self, image: np.ndarray, format: str = 'jpeg') -> str:
        # 检查图像是否是3通道的，并转换为RGB格式（如果是jpeg）
        if format == 'jpeg':
            if len(image.shape) == 3 and image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        # 根据指定格式进行编码
        if format.lower() == 'jpeg':
            success, buffer = cv2.imencode('.jpg', image)
        elif format.lower() == 'png':
            success, buffer = cv2.imencode('.png', image)
        else:
            raise ValueError("不支持的图像格式: 支持的格式有 'jpeg' 和 'png'")
        
        if not success:
            raise RuntimeError("图像编码失败")
        # 转换为base64字符串
        base64_str = base64.b64encode(buffer).decode('utf-8')
        mime_type = 'image/jpeg' if format == 'jpeg' else 'image/png'
        return f"data:{mime_type};base64,{base64_str}".split(",")[-1]

    def base64_to_image(self, base64_str: str) -> np.ndarray:
        # 检查是否存在data URI scheme前缀并去除
        if base64_str.startswith('data:image'):
            base64_str = base64_str.split(',', 1)[1]
        # 解码base64字符串
        img_data = base64.b64decode(base64_str)
        # 将二进制数据转换为numpy数组
        np_arr = np.frombuffer(img_data, np.uint8)
        # 解码图像
        image = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
        # 如果是None，则表示解码失败
        if image is None:
            raise ValueError("无法从提供的Base64字符串中解码图像")
            
        return image
    
    import re

    def format_number_string(self, money_list, data_type='list'):
        if data_type == 'list':
            new_list = []
            for item in money_list:
                # 第一步：过滤掉所有非数字字符
                digits = ''.join(filter(str.isdigit, item))

                if not digits:
                    return "0.00"

                # 第二步：确保至少有两位数字用于小数部分
                if len(digits) < 2:
                    digits = '0' * (2 - len(digits)) + digits  # 补零到两位

                # 将最后两位作为小数部分
                integer_part = digits[:-2]
                decimal_part = digits[-2:]

                # 第三步：格式化整数部分：每三位加逗号
                formatted_integer = "{:,}".format(int(integer_part)) if integer_part else ""

                # 第四步：组合结果
                result = f"{formatted_integer}.{decimal_part}"
                new_list.append(result)
            return new_list
        else:
            digits = ''.join(filter(str.isdigit, money_list))

            if not digits:
                return "0.00"

            # 第二步：确保至少有两位数字用于小数部分
            if len(digits) < 2:
                digits = '0' * (2 - len(digits)) + digits  # 补零到两位

            # 将最后两位作为小数部分
            integer_part = digits[:-2]
            decimal_part = digits[-2:]

            # 第三步：格式化整数部分：每三位加逗号
            formatted_integer = "{:,}".format(int(integer_part)) if integer_part else ""

            # 第四步：组合结果
            result = f"{formatted_integer}.{decimal_part}"
            return result

    def res_postprocess(self, res_dict):
        print(res_dict)
        result = {
            "contractNo":res_dict['contractNo'] if 'contractNo' in res_dict else [],
            "contractAmount":res_dict['contractAmount'] if 'contractAmount' in res_dict else [], #
            "cumulativeProgress":[
                {"completPercentage":res_dict['completPercentage'] if 'completPercentage' in res_dict else []}, #
                {"cumulativeAmount":res_dict['cumulativeAmount'] if 'cumulativeAmount' in res_dict else []} # 
                ],
            "paymentProportion":res_dict['paymentProportion'] if 'paymentProportion' in res_dict else [],
            "assessmentInf":[
                {"assessmentScore":res_dict['assessmentScore'] if 'assessmentScore' in res_dict else []},
                {"assessmentPeriod":res_dict['assessmentPeriod'] if 'assessmentPeriod' in res_dict else []},
                {"assessmentDeduction":res_dict['assessmentDeduction'] if 'assessmentDeduction' in res_dict else []}
                ],
            "cumulativePaidAmount":res_dict['cumulativePaidAmount'] if 'cumulativePaidAmount' in res_dict else [], #
            "settlementType":res_dict['settlementType'] if 'settlementType' in res_dict else [],
            "mergeCumul":["单行"],
            "sealNum":res_dict['seal'] if 'seal' in res_dict else "0",
            "signature":res_dict['signature'] if 'signature' in res_dict else "0",
            "settlementAmount(noTax)":res_dict['settlementAmount_noTax'] if 'settlementAmount_noTax' in res_dict else [], #
            "settlementAmount(Tax)":res_dict['settlementAmount_Tax'] if 'settlementAmount_Tax' in res_dict else [], #
            "totalSettlementAmount(noTax)":res_dict['totalSettlementAmount_noTax'] if 'totalSettlementAmount_noTax' in res_dict else "", #
            "totalSettlementAmount(Tax)":res_dict['totalSettlementAmount_Tax'] if 'totalSettlementAmount_Tax' in res_dict else "" #
        }

        # 对特殊字段进行处理,转化为金额
        result['contractAmount'] = self.format_number_string(result['contractAmount'])
        result['cumulativeProgress'][1]['cumulativeAmount'] = self.format_number_string(result['cumulativeProgress'][1]['cumulativeAmount'])
        result['cumulativePaidAmount'] = self.format_number_string(result['cumulativePaidAmount'])
        result['settlementAmount(noTax)'] = self.format_number_string(result['settlementAmount(noTax)'])
        result['settlementAmount(Tax)'] = self.format_number_string(result['settlementAmount(Tax)'])
        result['totalSettlementAmount(noTax)'] = self.format_number_string(result['totalSettlementAmount(noTax)'], 'str')
        result['totalSettlementAmount(Tax)'] = self.format_number_string(result['totalSettlementAmount(Tax)'], 'str')

        if len(result['cumulativePaidAmount']) < len(result['settlementAmount(noTax)']):
            result["mergeCumul"] = '多行'

        return result

    def infer_res(self, img):
        yolo_res = self.yolo.infer_base(img)
        res_dict = {
            'signature': 0, 
            'seal': 0, 
            'totalSettlementAmount_noTax': [], 
            'totalSettlementAmount_Tax': [], 
            'settlementAmount_noTax': [], 
            'settlementType': [], 
            'contractNo': [], 
            'assessmentPeriod': [], 
            'settlementAmount_Tax': [], 
            'cumulativePaidAmount': [], 
            'cumulativeAmount': [], 
            'contractAmount': [], 
            'assessmentScore': [], 
            'assessmentDeduction': [], 
            'completPercentage': [], 
            'paymentProportion': []
        }
        index = 0
        for k,v in yolo_res.items():
            # for item in v:
            #     cv2.imwrite(f'./tmp/{k}_{index}.jpg', item)
            #     index  += 1
            if k in self.goal1:
                for item in v:
                    data = self.image_to_base64(item)
                    ocr_res = self.ocr.ocr_base(data)
                    res_dict[k].append(ocr_res)
                    print(k, ocr_res)
            elif k in self.goal2:
                for item in v:
                    data = self.image_to_base64(item)
                    ocr_res = self.ocr.ocr_with_det(data)
                    res_dict[k].append(ocr_res)
                    print(k, ocr_res)
            else:
                res_dict[k] = len(v)

            if k in ['totalSettlementAmount_noTax', 'totalSettlementAmount_Tax']:
                res_dict[k] = res_dict[k][0]
        res = self.res_postprocess(res_dict)
        return res
    
    def infer_flow(self, post_map):
        try:
            t0 = time.time()
            tip1, img_cv = self.check_post_data(post_map)
            if not tip1:
                return img_cv
            ocr_res = self.infer_res(img_cv)
            api_res = {
                "isSuc": True,
                "code": 0,
                "msg": str(time.time() - t0)[:5] + 's',
                "res": ocr_res
            }
            logger.info(api_res)
            return Response(json.dumps(api_res, ensure_ascii=False), mimetype='application/json')
        except Exception as e:
            traceback.print_exc(e)
            logger.error(e)
            fail_res = {
                "res": None,
                "code": 30000,
                "msg": "普通识别错误"
            }
            return Response(json.dumps(fail_res, ensure_ascii=False), mimetype='application/json')

if __name__ == '__main__':
    chengben = ChengBen()
    img_path = r"C:\Users\<USER>\Desktop\财务域能力开发-cbl\成本结算表\chengben\jiesuan_0179.jpg"
    img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
    print(chengben.infer_res(img))