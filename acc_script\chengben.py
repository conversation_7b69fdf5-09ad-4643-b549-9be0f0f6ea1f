# -*- coding: utf-8 -*-
# @Time    : 2024/05/09 14:21
# <AUTHOR> Chen 
# @File    : 成本结算表.py
# @Software: PyCharm
# @Desc    :
import hashlib
import os
import uuid,time
import json
import base64

import pandas as pd
import requests

def file2base64(file_path):
    '''
    文件转base64
    '''
    image_base64 = ""
    with open(file_path, 'rb') as f:
        image = f.read()
        try:
            image_base64 = str(base64.b64encode(image), encoding='utf-8')
        except Exception as e:
            image_base64 = base64.b64encode(image).decode()
    # with open('1.txt', 'w') as f:
    #     f.write(image_base64)
    return image_base64

def predict(fpath):
    print(fpath)
    api = 'http://127.0.0.1:8083/OcrWeb/azApi/costSettlementSum_ocrd'
    data = {
        'base64_strs': file2base64(fpath)
    }
    data = json.dumps(data)
    hdr = {'Content-Type': 'application/json'}
    res = requests.post(api, data=data, headers=hdr)
    res = json.loads(res.text)
    res = res['res']
    print(res)
    return res

# print(predict(r'E:\myproj\2024-05-09-译图能力测试\样本\成本结算表\jiesuan_0542.jpg'))

# predict(r'E:\myproj\2024-05-09-译图能力测试\样本\成本结算表\jiesuan_0175.jpg')
file_name = []
contract_no = []
contract_money = []
over_percent = []
total_money = []
pay_percent = []
examine_score = []
examine_date = []
examine_deduct = []
no_tax_money = []
tax_money = []
total_pay_money = []
jiesuan_type = []
this_no_tax_money = []
this_tax_money = []
stamp_num = []
sign_num = []
cost_time = []

df = pd.read_excel(r"D:\myproj\能力测试\2024-05-09-译图能力测试\empty.xlsx")
img_dir = r"D:\myproj\2025\caiwuyu\acc_img\chengben"
for fname in os.listdir(img_dir):
    fpath = os.path.join(img_dir, fname)
    t0 = time.time()
    res = predict(fpath)
    if len(res) == 0:
        file_name.append(str(fname))
        contract_no.append(str(''))
        contract_money.append(str(''))
        over_percent.append(str(''))
        total_money.append(str(''))
        pay_percent.append(str(''))
        examine_score.append(str(''))
        examine_date.append(str(''))
        examine_deduct.append(str(''))
        no_tax_money.append(str(''))
        tax_money.append(str(''))
        total_pay_money.append(str(''))
        jiesuan_type.append(str(''))
        this_no_tax_money.append(str(''))
        this_tax_money.append(str(''))
        stamp_num.append(str(''))
        sign_num.append(str(''))
        cost_time.append(str(time.time() - t0))
    else:
        file_name.append(str(fname))
        contract_no.append(str(res['contractNo']))
        contract_money.append(str(res['contractAmount']))
        over_percent.append(str(res['cumulativeProgress'][0]['completPercentage']))
        total_money.append(str(res['cumulativeProgress'][1]['cumulativeAmount']))
        pay_percent.append(str(res['paymentProportion']))
        examine_score.append(str(res['assessmentInf'][0]['assessmentScore']))
        examine_date.append(str(res['assessmentInf'][1]['assessmentPeriod']))
        examine_deduct.append(str(res['assessmentInf'][2]['assessmentDeduction']))
        no_tax_money.append(str(res['settlementAmount(noTax)']))
        tax_money.append(str(res['settlementAmount(Tax)']))
        total_pay_money.append(str(res['cumulativePaidAmount']))
        jiesuan_type.append(str(res['settlementType']))
        this_no_tax_money.append(str(res['settlementAmount(noTax)']))
        this_tax_money.append(str(res['settlementAmount(Tax)']))
        stamp_num.append(str(res['sealNum']))
        sign_num.append(str(res['signature']))
        cost_time.append(str(time.time()-t0))
    print(fname, time.time()-t0)

df['文件名'] = file_name
df['合同号码'] = contract_no
df['合同含税金额'] = contract_money
df['完工百分比'] = over_percent
df['累计完成金额'] = total_money
df['本次付款比例百分比'] = pay_percent
df['考核分数'] = examine_score
df['考核期间'] = examine_date
df['考核扣款'] = examine_deduct
df['不含税结算金额'] = no_tax_money
df['含税结算金额'] = tax_money
df['累计已付金额（不含税）'] = total_pay_money
df['结算类型'] = jiesuan_type
df['本次结算金额合计（不含税）'] = this_no_tax_money
df['本次结算金额合计（含税）'] = this_tax_money
df['公章数量'] = stamp_num
df['签名数量'] = sign_num
df.to_excel('成本结算表.xlsx', index=False)

