# -*- coding: utf-8 -*-
# @Time    : 2024/05/21 17:39
# <AUTHOR> Chen
# @File    : cal.py
# @Software: PyCharm
# @Desc    : 比对两个Excel文件的准确率
import pandas as pd
import os
import ast
import re

def parse_amount_list(value):
    """
    解析file1中的金额列表格式，如 "['6,566.00', '1,937.62']"
    返回数值列表的总和
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0.0

    try:
        # 转换为字符串并清理
        value_str = str(value).strip()

        # 如果是列表格式
        if value_str.startswith('[') and value_str.endswith(']'):
            # 使用ast.literal_eval安全解析列表
            amount_list = ast.literal_eval(value_str)
            total = 0.0
            for amount in amount_list:
                if amount and str(amount).strip():
                    # 移除逗号并转换为浮点数
                    clean_amount = str(amount).replace(',', '').strip()
                    if clean_amount:
                        total += float(clean_amount)
            return total
        else:
            # 如果不是列表格式，直接处理为单个数值
            clean_value = value_str.replace(',', '').strip()
            return float(clean_value) if clean_value else 0.0

    except (ValueError, SyntaxError, TypeError) as e:
        print(f"警告：解析金额时出错 '{value}': {e}")
        return 0.0

def parse_single_amount(value):
    """
    解析file2中的单个金额格式，如 "7738.56"
    """
    if pd.isna(value) or value == '' or str(value).strip() == '':
        return 0.0

    try:
        # 转换为字符串并清理
        value_str = str(value).replace(',', '').strip()
        return float(value_str) if value_str else 0.0
    except (ValueError, TypeError) as e:
        print(f"警告：解析单个金额时出错 '{value}': {e}")
        return 0.0

def compare_special_columns(df1_value, df2_value, col_name):
    """
    比较特殊列（金额合计列）
    """
    # 特殊处理的列名
    special_columns = {'本次结算金额合计（不含税）', '本次结算金额合计（含税）'}

    if col_name in special_columns:
        # file1的值需要解析列表并求和
        amount1 = parse_amount_list(df1_value)
        # file2的值直接解析
        amount2 = parse_single_amount(df2_value)

        # 比较两个金额（允许小的误差，如0.01）
        return abs(amount1 - amount2) < 0.01
    else:
        # 普通列的比较
        return str(df1_value) == str(df2_value)

def compare_two_excel_files(file1_path, file2_path, sheet1_name='Sheet1', sheet2_name='Sheet1'):
    """
    比对两个Excel文件的准确率

    参数:
    file1_path: 第一个Excel文件路径
    file2_path: 第二个Excel文件路径
    sheet1_name: 第一个文件的工作表名
    sheet2_name: 第二个文件的工作表名

    返回:
    comparison_results: 比对结果字典
    """

    # 检查文件是否存在
    if not os.path.exists(file1_path):
        print(f"错误：文件不存在 - {file1_path}")
        return None

    if not os.path.exists(file2_path):
        print(f"错误：文件不存在 - {file2_path}")
        return None

    try:
        # 加载两个Excel文件
        print(f"正在读取第一个文件：{file1_path}")
        df1 = pd.read_excel(file1_path, sheet_name=sheet1_name)

        print(f"正在读取第二个文件：{file2_path}")
        df2 = pd.read_excel(file2_path, sheet_name=sheet2_name)

        # 检查两个文件的行数和列数
        print(f"第一个文件：{len(df1)}行，{len(df1.columns)}列")
        print(f"第二个文件：{len(df2)}行，{len(df2.columns)}列")

        # 确保两个文件有相同的列
        common_columns = set(df1.columns) & set(df2.columns)

        # 排除'文件名'列不参与比对
        exclude_columns = {'文件名'}
        common_columns = common_columns - exclude_columns

        if not common_columns:
            print("错误：两个文件没有相同的列名（排除'文件名'列后）")
            return None

        print(f"将比对以下列：{sorted(list(common_columns))}")
        print(f"排除的列：{list(exclude_columns)}")

        # 取较小的行数进行比对
        min_rows = min(len(df1), len(df2))
        if len(df1) != len(df2):
            print(f"警告：两个文件的行数不同，将只比对前{min_rows}行")

        # 比较两个文件的每一列数据相同的数量
        comparison_results = {}
        total_same = 0
        total_items = 0

        for col in sorted(common_columns):
            # 只比对前min_rows行
            df1_col = df1[col].iloc[:min_rows]
            df2_col = df2[col].iloc[:min_rows]

            # 使用特殊比较函数
            same_values = []
            for i in range(min_rows):
                df1_value = df1_col.iloc[i]
                df2_value = df2_col.iloc[i]
                is_same = compare_special_columns(df1_value, df2_value, col)
                same_values.append(is_same)

            # 计算相同数据的数量
            num_same_values = sum(same_values)
            total_same += num_same_values
            total_items += min_rows

            # 计算准确率
            accuracy = (num_same_values / min_rows) * 100 if min_rows > 0 else 0

            comparison_results[col] = {
                '相同数量': num_same_values,
                '总数量': min_rows,
                '准确率': accuracy
            }

        return comparison_results, total_same, total_items

    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return None

def print_results_in_chinese(comparison_results, total_same, total_items):
    """
    用中文打印比对结果
    """
    print("\n" + "="*60)
    print("                    准确率比对结果")
    print("="*60)

    # 特殊处理的列名
    special_columns = {'本次结算金额合计（不含税）', '本次结算金额合计（含税）'}

    for col, result in comparison_results.items():
        num_same = result['相同数量']
        total_count = result['总数量']
        accuracy = result['准确率']

        # 标记特殊列
        special_mark = " [特殊处理]" if col in special_columns else ""

        # print(f"列名：{col}{special_mark}")
        print(f"列名：{col}，准确率：{accuracy:.2f}%")

    # 计算总体准确率
    overall_accuracy = (total_same / total_items) * 100 if total_items > 0 else 0

    print(f"总相同数量：{total_same}个")
    print(f"总比对数量：{total_items}个")
    print(f"总体准确率：{overall_accuracy:.2f}%")
    print("="*60)

def main():
    """
    主函数
    """
    # 配置文件路径
    file1_path = r"D:\myproj\2025\caiwuyu\成本结算表.xlsx"  # 第一个Excel文件路径
    # file1_path = r"D:\myproj\能力测试\2024-05-09-译图能力测试\result\新建 XLSX 工作表.xlsx"
    file2_path = r"D:\myproj\2025\caiwuyu\acc_img\成本结算表-标注信息.xlsx"  # 第二个Excel文件路径

    # 配置工作表名称
    sheet1_name = 'Sheet1'  # 第一个文件的工作表名
    sheet2_name = '标注内容'  # 第二个文件的工作表名

    print("开始比对两个Excel文件的准确率...")
    print(f"文件1：{file1_path}")
    print(f"文件2：{file2_path}")
    print(f"工作表1：{sheet1_name}")
    print(f"工作表2：{sheet2_name}")

    # 执行比对
    result = compare_two_excel_files(file1_path, file2_path, sheet1_name, sheet2_name)

    if result is not None:
        comparison_results, total_same, total_items = result

        # 打印中文结果
        print_results_in_chinese(comparison_results, total_same, total_items)

        print("\n比对完成！")
    else:
        print("比对失败，请检查文件路径和格式。")

if __name__ == '__main__':
    main()

'''
译图
列名：不含税结算金额 ，准确率：58.00%
列名：公章数量 ，准确率：70.00%
列名：合同号码 ，准确率：19.00%
列名：合同含税金额 ，准确率：56.00%
列名：含税结算金额 ，准确率：53.00%
列名：完工百分比 ，准确率：64.00%
列名：本次付款比例百分比 ，准确率：67.00%
列名：本次结算金额合计（不含税） ，准确率：0.00%
列名：本次结算金额合计（含税） ，准确率：0.00%
列名：签名数量 ，准确率：86.00%
列名：累计完成金额 ，准确率：55.00%
列名：累计已付金额（不含税） ，准确率：54.00%
列名：结算类型 ，准确率：68.00%
列名：考核分数 ，准确率：55.00%
列名：考核扣款 ，准确率：59.00%
列名：考核期间 ，准确率：63.00%
总相同数量：827个
总比对数量：1600个
总体准确率：51.69%

列名：不含税结算金额 ，准确率：68.00%
列名：公章数量 ，准确率：100.00%
列名：合同号码 ，准确率：25.00%
列名：合同含税金额 ，准确率：55.00%
列名：含税结算金额 ，准确率：67.00%
列名：完工百分比 ，准确率：76.00%
列名：本次付款比例百分比 ，准确率：78.00%
列名：本次结算金额合计（不含税） ，准确率：0.00%
列名：本次结算金额合计（含税） ，准确率：0.00%
列名：签名数量 ，准确率：89.00%
列名：累计完成金额 ，准确率：65.00%
列名：累计已付金额（不含税） ，准确率：59.00%
列名：结算类型 ，准确率：82.00%
列名：考核分数 ，准确率：55.00%
列名：考核扣款 ，准确率：64.00%
列名：考核期间 ，准确率：56.00%
总相同数量：939个
总比对数量：1600个
总体准确率：58.69%

列名：不含税结算金额，准确率：66.00%
列名：公章数量，准确率：100.00%
列名：合同号码，准确率：24.00%
列名：合同含税金额，准确率：60.00%
列名：含税结算金额，准确率：62.00%
列名：完工百分比，准确率：75.00%
列名：本次付款比例百分比，准确率：76.00%
列名：本次结算金额合计（不含税），准确率：70.00%
列名：本次结算金额合计（含税），准确率：70.00%
列名：签名数量，准确率：98.00%
列名：累计完成金额，准确率：62.00%
列名：累计已付金额（不含税），准确率：56.00%
列名：结算类型，准确率：73.00%
列名：考核分数，准确率：42.00%
列名：考核扣款，准确率：55.00%
列名：考核期间，准确率：56.00%
总相同数量：1045个
总比对数量：1600个
总体准确率：65.31%
'''