from ultralytics import YOLO
from PIL import Image, ImageDraw

model = YOLO(r'./weights/dailishang0529.pt')

img_path = r"D:\myproj\2025\caiwuyu\acc_img\dailishang\9150.jpg"
img = Image.open(img_path)
draw = ImageDraw.Draw(img)
res = model(img_path, iou=0.35, conf=0.5)[0]

label = ['agent_signature','agent_date','agent_seal','agent_handprint','refundMoney','refundName','refundAccount','bank','account','company_signature','company_date','company_seal','company_handprint','company_comment1','company_comment2','company_comment3','other_recycle_signature','other_recycle_date','other_recycle_seal','other_recycle_handprint','other_recycle_comment1','other_recycle_comment2','other_fine_signature','other_fine_date','other_fine_seal','other_fine_handprint','other_fine_comment1','other_fine_comment2','director_signature','director_date','director_seal','director_handprint','director_comment1','director_comment2','director_comment3','manage_signature','manage_date','manage_seal','manage_handprint','manage_comment1','manage_comment2','manage_comment3']

for box in res.boxes:
    cls_name = label[int(box.cls)]
    print(cls_name)
    score = box.conf.item()
    # print(score)
    draw.rectangle([(int(box.xyxy[0][0]), int(box.xyxy[0][1])), (int(box.xyxy[0][2]), int(box.xyxy[0][3]))], outline='red', width=1)
    draw.text((int(box.xyxy[0][0]), int(box.xyxy[0][1])), cls_name, fill='red')
    draw.text((int(box.xyxy[0][0]), int(box.xyxy[0][3])), str(score)[:4], fill='red')
    
img.save('./out1.jpg')

# res = model(img_path, iou=0.35, conf=0.1)[0]
# res.show()
