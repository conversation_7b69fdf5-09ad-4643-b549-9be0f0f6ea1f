# -*- coding: utf-8 -*-
# @Time    : 2024/05/09 15:18
# <AUTHOR> Chen 
# @File    : cal.py
# @Software: PyCharm
# @Desc    : 读取两个Excel文件进行比对
import pandas as pd
import os

def compare_excel_files(original_file, new_file, original_sheet='Sheet1', new_sheet='标注内容'):
    """
    比对两个Excel文件
    original_file: 原始文件路径（第2列对应第16列）
    new_file: 新文件路径（第2列对应第2列）
    """
    
    # 初始化计数器
    refund_money_num = 0
    refund_reason_num = 0
    refund_name_num = 0
    refund_account_num = 0
    bank_num = 0
    bank_account_num = 0
    dot_sign_num = 0
    dot_date_num = 0
    dot_stamp_num = 0
    dot_finger_num = 0
    channel_num = 0
    channel_code_num = 0
    yidong_audit_num = 0
    yidong_sign_num = 0
    yidong_date_num = 0
    yidong_stamp_num = 0
    yidong_finger_num = 0
    grid_audit_num = 0
    grid_sign_num = 0
    grid_date_num = 0
    grid_stamp_num = 0
    grid_finger_num = 0
    manager_audit_num = 0
    manager_sign_num = 0
    manager_date_num = 0
    manager_stamp_num = 0
    manager_finger_num = 0
    
    # 读取原始文件
    print(f"读取原始文件: {original_file}")
    df_original = pd.read_excel(original_file, sheet_name=original_sheet)
    
    # 读取新文件
    print(f"读取新文件: {new_file}")
    df_new = pd.read_excel(new_file, sheet_name=new_sheet)
    
    # 确保两个文件的行数相同
    if len(df_original) != len(df_new):
        print(f"警告: 两个文件的行数不同! 原始文件: {len(df_original)}, 新文件: {len(df_new)}")
        min_rows = min(len(df_original), len(df_new))
        print(f"将只比对前 {min_rows} 行")
    else:
        min_rows = len(df_original)
    
    # 逐行比对
    for index in range(min_rows):
        original_item = df_original.iloc[index]
        new_item = df_new.iloc[index]
        
        # 获取类型信息（假设都在第1列，索引为1）
        item_type = original_item.iloc[1] if len(original_item) > 1 else ""
        
        if item_type == '网点填写':
            # 原始文件：第2列对应第16列，新文件：第2列对应第2列
            if str(original_item.iloc[2]) == str(new_item.iloc[2]):
                refund_money_num += 1
            if str(original_item.iloc[3]) == str(new_item.iloc[3]):
                refund_reason_num += 1
            if str(original_item.iloc[4]) == str(new_item.iloc[4]):
                refund_name_num += 1
            if str(original_item.iloc[5]) == str(new_item.iloc[5]):
                refund_account_num += 1
            if str(original_item.iloc[6]) == str(new_item.iloc[6]):
                bank_num += 1
            if str(original_item.iloc[7]) == str(new_item.iloc[7]):
                bank_account_num += 1
            if str(original_item.iloc[11]) == str(new_item.iloc[11]):
                dot_sign_num += 1
            if str(original_item.iloc[12]) == str(new_item.iloc[12]):
                dot_date_num += 1
            if str(original_item.iloc[13]) == str(new_item.iloc[13]):
                dot_stamp_num += 1
            if str(original_item.iloc[14]) == str(new_item.iloc[14]):
                dot_finger_num += 1
                
        elif item_type == '移动公司填写':
            if str(original_item.iloc[8]) == str(new_item.iloc[8]):
                channel_num += 1
            if str(original_item.iloc[9]) == str(new_item.iloc[9]):
                channel_code_num += 1
            if str(original_item.iloc[10]) == str(new_item.iloc[10]):
                yidong_audit_num += 1
            if str(original_item.iloc[11]) == str(new_item.iloc[11]):
                yidong_sign_num += 1
            if str(original_item.iloc[12]) == str(new_item.iloc[12]):
                yidong_date_num += 1
            if str(original_item.iloc[13]) == str(new_item.iloc[13]):
                yidong_stamp_num += 1
            if str(original_item.iloc[14]) == str(new_item.iloc[14]):
                yidong_finger_num += 1
                
        elif '网格总监' in str(item_type):
            if str(original_item.iloc[10]) == str(new_item.iloc[10]):
                grid_audit_num += 1
            if str(original_item.iloc[11]) == str(new_item.iloc[11]):
                grid_sign_num += 1
            if str(original_item.iloc[12]) == str(new_item.iloc[12]):
                grid_date_num += 1
            if str(original_item.iloc[13]) == str(new_item.iloc[13]):
                grid_stamp_num += 1
            if str(original_item.iloc[14]) == str(new_item.iloc[14]):
                grid_finger_num += 1
                
        elif item_type == "部门经理审核":
            if str(original_item.iloc[10]) == str(new_item.iloc[10]):
                manager_audit_num += 1
            if str(original_item.iloc[11]) == str(new_item.iloc[11]):
                manager_sign_num += 1
            if str(original_item.iloc[12]) == str(new_item.iloc[12]):
                manager_date_num += 1
            if str(original_item.iloc[13]) == str(new_item.iloc[13]):
                manager_stamp_num += 1
            if str(original_item.iloc[14]) == str(new_item.iloc[14]):
                manager_finger_num += 1
    
    # 打印结果
    print(f"退款金额：{refund_money_num}/100={refund_money_num}%")
    print(f"退款原因：{refund_reason_num}/100={refund_reason_num}%")
    print(f"申请退款方名称：{refund_name_num}/100={refund_name_num}%")
    print(f"申请退款方账户名：{refund_account_num}/100={refund_account_num}%")
    print(f"开户行：{bank_num}/100={bank_num}%")
    print(f"银行账号：{bank_account_num}/100={bank_account_num}%")
    print(f"网点签名：{dot_sign_num}/100={dot_sign_num}%")
    print(f"网点日期：{dot_date_num}/100={dot_date_num}%")
    print(f"网点公章：{dot_stamp_num}/100={dot_stamp_num}%")
    print(f"网点手印：{dot_finger_num}/100={dot_finger_num}%")
    print(f"渠道名称：{channel_num}/100={channel_num}%")
    print(f"渠道编码：{channel_code_num}/100={channel_code_num}%")
    print(f"移动审核意见：{yidong_audit_num}/100={yidong_audit_num}%")
    print(f"移动签名：{yidong_sign_num}/100={yidong_sign_num}%")
    print(f"移动日期：{yidong_date_num}/100={yidong_date_num}%")
    print(f"移动公章：{yidong_stamp_num}/100={yidong_stamp_num}%")
    print(f"移动手印：{yidong_finger_num}/100={yidong_finger_num}%")
    print(f"网格意见：{grid_audit_num}/100={grid_audit_num}%")
    print(f"网格签名：{grid_sign_num}/100={grid_sign_num}%")
    print(f"网格日期：{grid_date_num}/100={grid_date_num}%")
    print(f"网格公章：{grid_stamp_num}/100={grid_stamp_num}%")
    print(f"网格手印：{grid_finger_num}/100={grid_finger_num}%")
    print(f"部门意见：{manager_audit_num}/100={manager_audit_num}%")
    print(f"部门签名：{manager_sign_num}/100={manager_sign_num}%")
    print(f"部门日期：{manager_date_num}/100={manager_date_num}%")
    print(f"部门公章：{manager_stamp_num}/100={manager_stamp_num}%")
    print(f"部门手印：{manager_finger_num}/100={manager_finger_num}%")
    
    # 计算总体准确率
    total_correct = (refund_money_num + refund_reason_num + refund_name_num + refund_account_num + 
                    bank_num + bank_account_num + dot_sign_num + dot_date_num + dot_stamp_num + 
                    dot_finger_num + channel_num + channel_code_num + yidong_audit_num + 
                    yidong_sign_num + yidong_date_num + yidong_stamp_num + yidong_finger_num + 
                    grid_audit_num + grid_sign_num + grid_date_num + grid_stamp_num + 
                    grid_finger_num + manager_audit_num + manager_sign_num + manager_date_num + 
                    manager_stamp_num + manager_finger_num)
    
    total_items = 2700  # 假设总共2700个项目
    overall_accuracy = total_correct / total_items
    print(f"\n总体准确率：{total_correct}/{total_items}={overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)")
    
    return overall_accuracy

if __name__ == '__main__':
    # 文件路径配置
    original_file = r"D:\myproj\2025\caiwuyu\保证金退还1.xlsx"  # 原始文件（第2列对应第16列）
    new_file = r"D:\myproj\2025\caiwuyu\acc_img\保证金退还审批单-标注信息.xlsx"  # 新文件（第2列对应第2列）
    
    # 检查文件是否存在
    if not os.path.exists(original_file):
        print(f"错误: 原始文件不存在: {original_file}")
        exit(1)
    
    if not os.path.exists(new_file):
        print(f"错误: 新文件不存在: {new_file}")
        print("请将新文件路径修改为正确的路径")
        exit(1)
    
    # 执行比对
    try:
        accuracy = compare_excel_files(original_file, new_file)
        print(f"\n比对完成，总体准确率: {accuracy*100:.2f}%")
    except Exception as e:
        print(f"比对过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
'''
译图3.11
退款金额： 80
退款原因： 83
申请退款方名称： 84
申请退款方账户名： 91
开户行： 86
银行账号： 53
网点签名： 92
网点日期： 93
网点公章： 98
网点手印： 96
渠道名称： 90
渠道编码： 89
移动审核意见： 100
移动签名： 98
移动日期： 99
移动公章： 100
移动手印： 100
网格意见： 100
网格签名： 88
网格日期： 100
网格公章： 97
网格手印： 100
部门意见： 100
部门签名： 97
部门日期： 98
部门公章： 92
部门手印： 100
2504/2700=92.74%
'''


'''
退款金额：85/100=85%
退款原因：85/100=85%
申请退款方名称：80/100=80%
申请退款方账户名：90/100=90%
开户行：74/100=74%
银行账号：61/100=61%
网点签名：96/100=96%
网点日期：95/100=95%
网点公章：98/100=98%
网点手印：94/100=94%
渠道名称：82/100=82%
渠道编码：77/100=77%
移动审核意见：100/100=100%
移动签名：98/100=98%
移动日期：100/100=100%
移动公章：100/100=100%
移动手印：100/100=100%
网格意见：100/100=100%
网格签名：93/100=93%
网格日期：100/100=100%
网格公章：98/100=98%
网格手印：100/100=100%
部门意见：100/100=100%
部门签名：100/100=100%
部门日期：98/100=98%
部门公章：98/100=98%
部门手印：100/100=100%

总体准确率：2502/2700=0.9267 (92.67%)

比对完成，总体准确率: 92.67%
'''