# -*- coding: UTF-8 -*-
from flask import Flask, request, Response
import os
import json
import traceback
import cv2
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'

cv2.ocl.setUseOpenCL(False)   #设置opencv不使用多进程运行，但这句命令只在本作用域有效。
cv2.setNumThreads(0) 


task_name = os.environ.get('TASK_NAME', 'dailishang')
if task_name == 'baozhengjin':
    from baozhengjin import BaoZhengJin
    Task_engine = BaoZhengJin()
elif task_name == 'dailishang':
    from dailishang import DaiLiShang
    Task_engine = DaiLiShang()
elif task_name == 'chengben':
    from chengben import ChengBen
    Task_engine = ChengBen()
else:
    from baozhengjin import BaoZhengJin
    Task_engine = BaoZhengJin()



app = Flask(__name__)

# 保证金退还审批单
'''
{"result":{"isSuc":true,"code":0,"msg":"2.161s","res":[{"key":"网点填写","value":{"signature":2,"date":1,"seal":1,"handprint":0,"refundMoney":"5000","refundReason":"终止办理移动业务","refundName":"佛山市南海锋讯通讯配件店","refundAccount":"黎国忠","bank":"中国农业银行股份有限公司南海南方支行","account":"6228481462336150710"}},{"key":"移动公司填写","value":{"comment":"同意","signature":1,"date":1,"seal":0,"handprint":0,"channelName":"佛山市南海锋讯通讯配件店","channelCode":"EDFS17a953"}},{"key":"网格审核","value":{"comment":"同意","signature":1,"date":1,"seal":0,"handprint":0}},{"key":"部门经理审核","value":{"comment":"同意","signature":1,"date":1,"seal":0,"handprint":0}}]},"msg":"success","code":0}
'''
@app.route('/OcrWeb/azApi/depositRefundRecd', methods=['POST'])
def ag_depositrefundrecd():
    try:
        all_data = request.get_data() or None
        post_map = json.loads(all_data)
        api_res = Task_engine.infer_flow(post_map)
        return api_res
    except Exception as e:
        traceback.print_exc(e)
        fail_res = {
            "res": None,
            "code": 30000,
            "msg": "普通识别错误"
        }
        return Response(json.dumps(fail_res, ensure_ascii=False), mimetype='application/json')

# 集团代理商退出申请表
'''
{"result":{"isSuc":true,"code":0,"msg":"2.667s","res":[{"key":"代理商填写","value":{"signature":1,"date":1,"seal":1,"handprint":0,"refundMoney":"67145.42","refundName":"江西节点技术服务有限公司","refundAccount":"江西节点技术服务有限公司","bank":"招商银行股份有限公司深圳创维大厦支行","account":"***************"}},{"key":"移动公司填写","value":{"comment":"同意","signature":1,"date":1,"seal":0,"handprint":0}},{"key":"其他事项确认-回收","value":{"comment":"是","signature":0,"date":0,"seal":0,"handprint":0}},{"key":"其他事项确认-扣罚","value":{"comment":"是","signature":1,"date":1,"seal":0,"handprint":0}},{"key":"网格审核","value":{"comment":"同意","signature":1,"date":1,"seal":0,"handprint":0}},{"key":"部门经理审核","value":{"comment":"同意","signature":1,"date":1,"seal":0,"handprint":0}}]},"msg":"success","code":0}
'''
@app.route('/OcrWeb/azApi/businessAgentRecd', methods=['POST'])
def ag_businessagentrecd():
    try:
        all_data = request.get_data() or None
        post_map = json.loads(all_data)
        api_res = Task_engine.infer_flow(post_map)
        return api_res
    except Exception as e:
        traceback.print_exc(e)
        fail_res = {
            "res": None,
            "code": 30000,
            "msg": "普通识别错误"
        }
        return Response(json.dumps(fail_res, ensure_ascii=False), mimetype='application/json')

# 成本结算表
'''
{"isSuc":true,"code":0,"msg":"1.67s","res":{"contractNo":["CMD-DG-*********"],"contractAmount":["131,901.18"],"cumulativeProgress":[{"completPercentage":["99.96%"]},{"cumulativeAmount":["131,846.11"]}],"paymentProportion":["2%"],"assessmentInf":[{"assessmentScore":[""]},{"assessmentPeriod":[""]},{"assessmentDeduction":[""]}],"cumulativePaidAmount":["128,834.18"],"settlementType":["进度款"],"mergeCumul":["单行"],"sealNum":"1","signature":"4","settlementAmount(noTax)":["3,011.93"],"settlementAmount(Tax)":["3,283.00"],"totalSettlementAmount(noTax)":"3,011.93","totalSettlementAmount(Tax)":"3,283.00"}}
'''
@app.route('/OcrWeb/azApi/costSettlementSum_ocrd', methods=['POST'])
def ag_costsettlementsumocrd():
    try:
        all_data = request.get_data() or None
        post_map = json.loads(all_data)
        api_res = Task_engine.infer_flow(post_map)
        return api_res
    except Exception as e:
        traceback.print_exc(e)
        fail_res = {
            "res": None,
            "code": 30000,
            "msg": "普通识别错误"
        }
        return Response(json.dumps(fail_res, ensure_ascii=False), mimetype='application/json')

@app.route("/health")
def health():
    result = {'status': 'UP'}
    return Response(json.dumps(result), mimetype='application/json')


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8083, debug=False)  # 开启调试模式