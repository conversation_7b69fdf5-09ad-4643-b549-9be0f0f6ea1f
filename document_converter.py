"""
文档转图片工具类
支持 PDF 和 DOC/DOCX 文件的第一页转换为图片
Windows系统使用docx2pdf，Linux系统使用LibreOffice
"""
import base64
import tempfile
import os
import cv2
import numpy as np
import logging
import platform

# 设置日志
logger = logging.getLogger(__name__)

class DocumentConverter:
    """文档转图片转换器"""

    def __init__(self):
        """初始化转换器，检查依赖库"""
        self.pdf_available = False
        self.docx2pdf_available = False
        self.libreoffice_available = False
        self.is_linux = platform.system().lower() == 'linux'
        self.is_windows = platform.system().lower() == 'windows'

        # Linux系统特殊设置
        if self.is_linux:
            self._setup_linux_environment()

        # 检查 PDF 处理库
        try:
            import fitz  # PyMuPDF
            self.pdf_available = True
            self.pdf_lib = 'fitz'
            logger.info("使用 PyMuPDF 处理 PDF 文件")
        except ImportError:
            try:
                from pdf2image import convert_from_bytes
                self.pdf_available = True
                self.pdf_lib = 'pdf2image'
                logger.info("使用 pdf2image 处理 PDF 文件")
            except ImportError:
                logger.warning("未找到 PDF 处理库，请安装 PyMuPDF 或 pdf2image")

        # Windows系统：检查 docx2pdf 库
        if self.is_windows:
            try:
                import docx2pdf
                self.docx2pdf_available = True
                logger.info("docx2pdf 库可用")
            except ImportError:
                logger.warning("docx2pdf 库不可用，请安装: pip install docx2pdf")

        # Linux系统：检查 LibreOffice
        if self.is_linux:
            try:
                import subprocess
                cmd = ['libreoffice', '--headless', '--version']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.libreoffice_available = True
                    logger.info(f"LibreOffice 可用: {result.stdout.strip()}")
            except Exception as e:
                logger.warning(f"LibreOffice 不可用: {str(e)}")

        # 输出可用转换方案总结
        self._log_available_methods()

    def _log_available_methods(self):
        """记录可用的转换方案"""
        logger.info("=== 文档转换方案检查结果 ===")
        logger.info(f"操作系统: {platform.system()}")
        logger.info(f"PDF处理: {'可用 (' + self.pdf_lib + ')' if self.pdf_available else '不可用'}")

        if self.is_windows:
            logger.info(f"docx2pdf: {'可用' if self.docx2pdf_available else '不可用'}")
        elif self.is_linux:
            logger.info(f"LibreOffice: {'可用' if self.libreoffice_available else '不可用'}")

        # 安装建议
        if self.is_windows and not self.docx2pdf_available:
            logger.warning("Windows系统建议安装: pip install docx2pdf")
        elif self.is_linux and not self.libreoffice_available:
            logger.warning("Linux系统建议安装: sudo apt-get install libreoffice")

    def _setup_linux_environment(self):
        """设置Linux环境"""
        logger.info("检测到Linux系统，进行环境优化...")

        # 设置显示环境
        if not os.environ.get('DISPLAY'):
            os.environ['DISPLAY'] = ':99'
            logger.info("设置虚拟显示: :99")

        # 设置LibreOffice环境变量
        os.environ['SAL_USE_VCLPLUGIN'] = 'svp'  # 无头模式
        os.environ['SAL_DISABLE_OPENCL'] = '1'   # 禁用OpenCL

        # 设置字体路径
        font_paths = [
            '/usr/share/fonts',
            '/usr/local/share/fonts',
            os.path.expanduser('~/.fonts'),
            os.path.expanduser('~/.local/share/fonts')
        ]

        existing_paths = [path for path in font_paths if os.path.exists(path)]
        if existing_paths:
            os.environ['FONTCONFIG_PATH'] = ':'.join(existing_paths)
            logger.info(f"设置字体路径: {len(existing_paths)} 个目录")
    
    def pdf_to_image(self, pdf_data: bytes) -> np.ndarray:
        """
        将 PDF 第一页转换为图片
        
        Args:
            pdf_data: PDF 文件的字节数据
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        if not self.pdf_available:
            raise Exception("PDF 处理库不可用，请安装 PyMuPDF 或 pdf2image")
        
        try:
            if self.pdf_lib == 'fitz':
                return self._pdf_to_image_fitz(pdf_data)
            else:
                return self._pdf_to_image_pdf2image(pdf_data)
        except Exception as e:
            logger.error(f"PDF 转图片失败: {str(e)}")
            raise Exception(f"PDF 转图片失败: {str(e)}")
    
    def _pdf_to_image_fitz(self, pdf_data: bytes) -> np.ndarray:
        """使用 PyMuPDF 转换 PDF"""
        import fitz
        
        # 从字节数据创建 PDF 文档
        pdf_document = fitz.open(stream=pdf_data, filetype="pdf")
        
        if pdf_document.page_count == 0:
            raise Exception("PDF 文件为空")
        
        # 获取第一页
        page = pdf_document[0]
        
        # 设置渲染参数（提高分辨率）
        mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        # 转换为 PIL Image
        img_data = pix.tobytes("ppm")
        
        # 转换为 numpy 数组
        nparr = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        pdf_document.close()
        
        if img is None:
            raise Exception("PDF 页面渲染失败")
        
        return img
    
    def _pdf_to_image_pdf2image(self, pdf_data: bytes) -> np.ndarray:
        """使用 pdf2image 转换 PDF"""
        from pdf2image import convert_from_bytes
        from PIL import Image
        
        # 转换第一页
        images = convert_from_bytes(pdf_data, first_page=1, last_page=1, dpi=200)
        
        if not images:
            raise Exception("PDF 转换失败，未生成图片")
        
        # 获取第一页
        pil_image = images[0]
        
        # 转换为 OpenCV 格式
        img_array = np.array(pil_image)
        
        # PIL 使用 RGB，OpenCV 使用 BGR
        if len(img_array.shape) == 3:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        return img_array
    
    def doc_to_image(self, doc_data: bytes, file_type: str = "doc") -> np.ndarray:
        """
        将 DOC/DOCX 第一页转换为图片
        Windows系统使用docx2pdf，Linux系统使用LibreOffice

        Args:
            doc_data: DOC/DOCX 文件的字节数据
            file_type: 文件类型 ("doc" 或 "docx")

        Returns:
            np.ndarray: OpenCV 格式的图片数组

        Raises:
            Exception: 转换失败时抛出异常
        """
        if self.is_windows:
            # Windows系统：使用docx2pdf方案
            logger.info("Windows系统，使用docx2pdf转换方案")

            if not self.docx2pdf_available:
                raise Exception("docx2pdf库不可用，请安装: pip install docx2pdf")

            if not self.pdf_available:
                raise Exception("PDF处理库不可用，请安装 PyMuPDF 或 pdf2image")

            try:
                return self._doc_to_image_via_docx2pdf(doc_data)
            except Exception as e:
                raise Exception(f"docx2pdf转换失败: {str(e)}")

        elif self.is_linux:
            # Linux系统：使用LibreOffice方案
            logger.info("Linux系统，使用LibreOffice转换方案")

            if not self.libreoffice_available:
                raise Exception("LibreOffice不可用，请安装: sudo apt-get install libreoffice")

            if not self.pdf_available:
                raise Exception("PDF处理库不可用，请安装 PyMuPDF 或 pdf2image")

            try:
                return self._doc_to_image_via_libreoffice_pdf(doc_data)
            except Exception as e:
                raise Exception(f"LibreOffice转换失败: {str(e)}")

        else:
            # 其他系统
            raise Exception(f"不支持的操作系统: {platform.system()}，仅支持Windows和Linux")

    def _doc_to_image_via_docx2pdf(self, doc_data: bytes) -> np.ndarray:
        """使用docx2pdf库转换DOCX到PDF再转图片"""
        import tempfile
        from docx2pdf import convert

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as docx_file:
            docx_file.write(doc_data)
            docx_path = docx_file.name

        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as pdf_file:
            pdf_path = pdf_file.name

        try:
            # 转换DOCX到PDF
            logger.info(f"使用docx2pdf转换: {docx_path} -> {pdf_path}")
            convert(docx_path, pdf_path)

            # 检查PDF是否生成
            if not os.path.exists(pdf_path) or os.path.getsize(pdf_path) == 0:
                raise Exception("PDF文件未生成或为空")

            # 读取PDF并转换为图片
            with open(pdf_path, 'rb') as f:
                pdf_data = f.read()

            logger.info(f"PDF文件大小: {len(pdf_data)} 字节")
            return self.pdf_to_image(pdf_data)

        finally:
            # 清理临时文件
            try:
                os.unlink(docx_path)
                os.unlink(pdf_path)
            except:
                pass

    def _doc_to_image_via_libreoffice_pdf(self, doc_data: bytes) -> np.ndarray:
        """使用LibreOffice转换DOCX到PDF再转图片"""
        import subprocess
        import tempfile

        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            docx_path = os.path.join(temp_dir, "input.docx")
            pdf_path = os.path.join(temp_dir, "input.pdf")

            # 保存DOCX文件
            with open(docx_path, 'wb') as f:
                f.write(doc_data)

            # 构建LibreOffice命令（Linux优化）
            cmd = ['libreoffice']

            # 基础参数
            cmd.extend([
                '--headless',
                '--invisible',
                '--nodefault',
                '--nolockcheck',
                '--nologo',
                '--norestore'
            ])

            # Linux特定参数
            if self.is_linux:
                cmd.extend([
                    '--accept=socket,host=localhost,port=2002;urp;',
                    '--env:UserInstallation=file://' + temp_dir + '/libreoffice_profile'
                ])

            # 转换参数
            cmd.extend([
                '--convert-to', 'pdf',
                '--outdir', temp_dir,
                docx_path
            ])

            try:
                logger.info(f"执行LibreOffice命令: {' '.join(cmd)}")

                # 设置环境变量
                env = os.environ.copy()
                if self.is_linux:
                    env.update({
                        'SAL_USE_VCLPLUGIN': 'svp',
                        'SAL_DISABLE_OPENCL': '1',
                        'DISPLAY': env.get('DISPLAY', ':99')
                    })

                result = subprocess.run(cmd, capture_output=True, text=True,
                                      timeout=90, env=env, cwd=temp_dir)

                if result.returncode != 0:
                    raise Exception(f"LibreOffice转换失败 (返回码: {result.returncode}): {result.stderr}")

                # 检查PDF是否生成
                if not os.path.exists(pdf_path):
                    raise Exception("PDF文件未生成")

                if os.path.getsize(pdf_path) == 0:
                    raise Exception("生成的PDF文件为空")

                # 读取PDF并转换为图片
                with open(pdf_path, 'rb') as f:
                    pdf_data = f.read()

                logger.info(f"PDF文件大小: {len(pdf_data)} 字节")
                return self.pdf_to_image(pdf_data)

            except subprocess.TimeoutExpired:
                raise Exception("LibreOffice转换超时（90秒）")
            except FileNotFoundError:
                raise Exception("LibreOffice未安装或不在PATH中")

    def convert_document_to_image(self, base64_data: str, file_type: str) -> np.ndarray:
        """
        统一的文档转图片接口
        
        Args:
            base64_data: base64 编码的文件数据
            file_type: 文件类型 ("pdf", "doc", "docx")
            
        Returns:
            np.ndarray: OpenCV 格式的图片数组
            
        Raises:
            Exception: 转换失败时抛出异常
        """
        try:
            # 解码 base64 数据
            file_data = base64.b64decode(base64_data)
            
            if file_type.lower() == "pdf":
                return self.pdf_to_image(file_data)
            elif file_type.lower() in ["doc", "docx"]:
                return self.doc_to_image(file_data, file_type)
            else:
                raise Exception(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            logger.error(f"文档转换失败: {str(e)}")
            raise Exception(f"文档转换失败: {str(e)}")