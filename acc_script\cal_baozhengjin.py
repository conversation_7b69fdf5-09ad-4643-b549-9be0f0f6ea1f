# -*- coding: utf-8 -*-
# @Time    : 2024/05/09 15:18
# <AUTHOR> Chen 
# @File    : 保证金准确率.py
# @Software: PyCharm
# @Desc    :
import pandas as pd

"""
退款金额： 62%
退款原因： 0%
申请退款方名称： 82%
申请退款方账户名： 89%
开户行： 84%
银行账号： 50%
渠道名称： 87%
渠道编码： 67%
审核意见： 11%
签名数目： 55.5%
日期数目： 66.75%
公章数目： 87%
手印数目： 89.25%
平均时间： 6.205s
"""

# df = pd.read_excel(r'E:\myproj\2024-05-09-译图能力测试\output_保证金.xlsx', sheet_name='Sheet3')
# res = [0,0,0,0,0,0,0,0,0,0,0,0,0,0]
# for index,item in df.iterrows():
#     # 2 -> 16
#     # 14 -> 28
#     for i in range(2,16):
#         if str(item[i]) == str(item[i+14]) and str(item[i])!='-':
#             res[i-2] += 1
# print(res)
refund_money_num = 0
refund_reason_num = 0
refund_name_num = 0
refund_account_num = 0
bank_num = 0
bank_account_num = 0
dot_sign_num = 0
dot_date_num = 0
dot_stamp_num = 0
dot_finger_num = 0
channel_num = 0
channel_code_num = 0
yidong_audit_num = 0
yidong_sign_num = 0
yidong_date_num = 0
yidong_stamp_num = 0
yidong_finger_num = 0
grid_audit_num = 0
grid_sign_num = 0
grid_date_num = 0
grid_stamp_num = 0
grid_finger_num = 0
manager_audit_num = 0
manager_sign_num = 0
manager_date_num = 0
manager_stamp_num = 0
manager_finger_num = 0
df = pd.read_excel(r"D:\myproj\2025\caiwuyu\保证金退还.xlsx", sheet_name='Sheet1')
for index,item in df.iterrows():
    if item[1]=='网点填写':
        if str(item[2]) == str(item[16]):
            refund_money_num += 1
        if str(item[3]) == str(item[17]):
            refund_reason_num += 1
        if str(item[4]) == str(item[18]):
            refund_name_num += 1
        if str(item[5]) == str(item[19]):
            refund_account_num += 1
        if str(item[6]) == str(item[20]):
            bank_num += 1
        if str(item[7]) == str(item[21]):
            bank_account_num += 1
        if str(item[11]) == str(item[25]):
            dot_sign_num += 1
        if str(item[12]) == str(item[26]):
            dot_date_num += 1
        if str(item[13]) == str(item[27]):
            dot_stamp_num += 1
        if str(item[14]) == str(item[28]):
            dot_finger_num += 1
    if item[1] == '移动公司填写':
        if str(item[8]) == str(item[22]):
            channel_num += 1
        if str(item[9]) == str(item[23]):
            channel_code_num += 1
        if str(item[10]) == str(item[24]):
            yidong_audit_num += 1
        if str(item[11]) == str(item[25]):
            yidong_sign_num += 1
        if str(item[12]) == str(item[26]):
            yidong_date_num += 1
        if str(item[13]) == str(item[27]):
            yidong_stamp_num += 1
        if str(item[14]) == str(item[28]):
            yidong_finger_num += 1
    if '网格总监' in item[1]:
        # print(str(item[10]), str(item[24]), str(item[10]) == str(item[24]))
        if str(item[10]) == str(item[24]):
            grid_audit_num += 1
        if str(item[11]) == str(item[25]):
            grid_sign_num += 1
        if str(item[12]) == str(item[26]):
            grid_date_num += 1
        if str(item[13]) == str(item[27]):
            grid_stamp_num += 1
        if str(item[14]) == str(item[28]):
            grid_finger_num += 1
    if item[1] == "部门经理审核":
        if str(item[10]) == str(item[24]):
            manager_audit_num += 1
        if str(item[11]) == str(item[25]):
            manager_sign_num += 1
        if str(item[12]) == str(item[26]):
            manager_date_num += 1
        if str(item[13]) == str(item[27]):
            manager_stamp_num += 1
        if str(item[14]) == str(item[28]):
            manager_finger_num += 1

print("退款金额：", refund_money_num)
print("退款原因：", refund_reason_num)
print("申请退款方名称：", refund_name_num)
print("申请退款方账户名：", refund_account_num)
print("开户行：", bank_num)
print("银行账号：", bank_account_num)
print("网点签名：", dot_sign_num)
print("网点日期：", dot_date_num)
print("网点公章：", dot_stamp_num)
print("网点手印：", dot_finger_num)
print("渠道名称：", channel_num)
print("渠道编码：", channel_code_num)
print("移动审核意见：", yidong_audit_num)
print("移动签名：", yidong_sign_num)
print("移动日期：", yidong_date_num)
print("移动公章：", yidong_stamp_num)
print("移动手印：", yidong_finger_num)
print("网格意见：", grid_audit_num)
print("网格签名：", grid_sign_num)
print("网格日期：", grid_date_num)
print("网格公章：", grid_stamp_num)
print("网格手印：", grid_finger_num)
print("部门意见：", manager_audit_num)
print("部门签名：", manager_sign_num)
print("部门日期：", manager_date_num)
print("部门公章：", manager_stamp_num)
print("部门手印：", manager_finger_num)

dd = refund_money_num + refund_reason_num + refund_name_num + refund_account_num + bank_num + bank_account_num + dot_sign_num + dot_date_num + dot_stamp_num + dot_finger_num + channel_num + channel_code_num + yidong_audit_num + yidong_sign_num + yidong_date_num + yidong_stamp_num + yidong_finger_num + grid_audit_num + grid_sign_num + grid_date_num + grid_stamp_num + grid_finger_num + manager_audit_num + manager_sign_num + manager_date_num + manager_stamp_num + manager_finger_num
print(dd/2700)

'''
译图3.11
退款金额： 80
退款原因： 83
申请退款方名称： 84
申请退款方账户名： 91
开户行： 86
银行账号： 53
网点签名： 92
网点日期： 93
网点公章： 98
网点手印： 96
渠道名称： 90
渠道编码： 89
移动审核意见： 100
移动签名： 98
移动日期： 99
移动公章： 100
移动手印： 100
网格意见： 100
网格签名： 88
网格日期： 100
网格公章： 97
网格手印： 100
部门意见： 100
部门签名： 97
部门日期： 98
部门公章： 92
部门手印： 100
2504/2700=92.74%
'''

'''
退款金额： 86
退款原因： 84
申请退款方名称： 78
申请退款方账户名： 92
开户行： 74
银行账号： 59
网点签名： 83
网点日期： 92
网点公章： 98
网点手印： 70
渠道名称： 86
渠道编码： 79
移动审核意见： 100
移动签名： 97
移动日期： 99
移动公章： 100
移动手印： 100
网格意见： 100
网格签名： 93
网格日期： 100
网格公章： 99
网格手印： 100
部门意见： 100
部门签名： 100
部门日期： 99
部门公章： 98
部门手印： 100
0.9133333333333333
'''