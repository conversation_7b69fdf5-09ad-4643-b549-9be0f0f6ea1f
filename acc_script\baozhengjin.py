# -*- coding: utf-8 -*-
# @Time    : 2024/05/09 10:01
# <AUTHOR> Chen 
# @File    : 保证金退还.py
# @Software: PyCharm
# @Desc    :
import hashlib
import os
import uuid,time
import json
import base64

import pandas as pd
import requests

def file2base64(file_path):
    '''
    文件转base64
    '''
    image_base64 = ""
    with open(file_path, 'rb') as f:
        image = f.read()
        try:
            image_base64 = str(base64.b64encode(image), encoding='utf-8')
        except Exception as e:
            image_base64 = base64.b64encode(image).decode()
    # with open('1.txt', 'w') as f:
    #     f.write(image_base64)
    return image_base64


def predict(fpath):
    print(fpath)
    api = 'http://127.0.0.1:8083/OcrWeb/azApi/depositRefundRecd'
    data = {
        'imgBase64': file2base64(fpath)
    }
    data = json.dumps(data)
    hdr = {'Content-Type': 'application/json'}
    res = requests.post(api, data=data, headers=hdr)
    res = json.loads(res.text)
    res = res['result']['res']
    print(res)
    return res




"""
填写类型 退款金额 退款原因 申请退款方名称 申请退款方账户名 开户行 银行账号 渠道名称 渠道编码 审核意见 签名数目 日期数目 公章数目 手印数目
"""

#
df = pd.read_excel(r"D:\myproj\能力测试\2024-05-09-译图能力测试\empty.xlsx")
res_file_name = []
res_fill_type = []
res_refund_money = []
res_refund_reason = []
res_refund_name = []
res_refund_account = []
res_bank = []
res_bank_no = []
res_channel = []
res_channel_no = []
res_audit = []
res_sign_num = []
res_date_num = []
res_stamp_num = []
res_finger_num = []
res_time = []


file_dir = r"D:\myproj\能力测试\2024-05-09-译图能力测试\样本\保证金退还审批单"
for filename in os.listdir(file_dir):
    file_path = os.path.join(file_dir, filename)
    t0 = time.time()
    myres = predict(file_path)
    t1 = time.time()
    if len(myres)==0:
        res_file_name.append(str(filename))
        res_time.append(str(t1 - t0))
        res_fill_type.append(str(''))
        res_refund_money.append(str(''))
        res_refund_reason.append(str(''))
        res_refund_name.append(str(''))
        res_refund_account.append(str(''))
        res_bank.append(str(''))
        res_bank_no.append(str(''))
        res_channel.append(str(''))
        res_channel_no.append(str(''))
        res_audit.append(str(''))
        res_sign_num.append(str(''))
        res_date_num.append(str(''))
        res_stamp_num.append(str(''))
        res_finger_num.append(str(''))
    else:
        for item in myres:
            if item['key'] == "网点填写":
                res_file_name.append(str(filename))
                res_time.append(str(t1 - t0))
                res_fill_type.append(str("网点填写"))
                res_refund_money.append(str(item['value']['refundMoney']))
                res_refund_reason.append(str(item['value']['refundReason']))
                res_refund_name.append(str(item['value']['refundName']))
                res_refund_account.append(str(item['value']['refundAccount']))
                res_bank.append(str(item['value']['bank']))
                res_bank_no.append(str(item['value']['account']))
                res_channel.append(str('-'))
                res_channel_no.append(str('-'))
                res_audit.append(str('-'))
                res_sign_num.append(str(item['value']['signature']))
                res_date_num.append(str(item['value']['date']))
                res_stamp_num.append(str(item['value']['seal']))
                res_finger_num.append(str(item['value']['handprint']))
            if item['key'] == "移动公司填写":
                res_file_name.append(str(filename))
                res_time.append(str(t1 - t0))
                res_fill_type.append(str("移动公司填写"))
                res_refund_money.append(str('-'))
                res_refund_reason.append(str('-'))
                res_refund_name.append(str('-'))
                res_refund_account.append(str('-'))
                res_bank.append(str('-'))
                res_bank_no.append(str('-'))
                res_channel.append(str(item['value']['channelName']))
                res_channel_no.append(str(item['value']['channelCode']))
                res_audit.append(str(item['value']['comment']))
                res_sign_num.append(str(item['value']['signature']))
                res_date_num.append(str(item['value']['date']))
                res_stamp_num.append(str(item['value']['seal']))
                res_finger_num.append(str(item['value']['handprint']))
            if item['key'] == "网格审核":
                res_file_name.append(str(filename))
                res_time.append(str(t1 - t0))
                res_fill_type.append(str("网格总监/室主任审核"))
                res_refund_money.append(str('-'))
                res_refund_reason.append(str('-'))
                res_refund_name.append(str('-'))
                res_refund_account.append(str('-'))
                res_bank.append(str('-'))
                res_bank_no.append(str('-'))
                res_channel.append(str('-'))
                res_channel_no.append(str('-'))
                res_audit.append(str(item['value']['comment']))
                res_sign_num.append(str(item['value']['signature']))
                res_date_num.append(str(item['value']['date']))
                res_stamp_num.append(str(item['value']['seal']))
                res_finger_num.append(str(item['value']['handprint']))
            if item['key'] == '部门经理审核':
                res_file_name.append(str(filename))
                res_time.append(str(t1 - t0))
                res_fill_type.append(str("部门经理审核"))
                res_refund_money.append(str('-'))
                res_refund_reason.append(str('-'))
                res_refund_name.append(str('-'))
                res_refund_account.append(str('-'))
                res_bank.append(str('-'))
                res_bank_no.append(str('-'))
                res_channel.append(str('-'))
                res_channel_no.append(str('-'))
                res_audit.append(str(item['value']['comment']))
                res_sign_num.append(str(item['value']['signature']))
                res_date_num.append(str(item['value']['date']))
                res_stamp_num.append(str(item['value']['seal']))
                res_finger_num.append(str(item['value']['handprint']))


df['文件名'] = res_file_name
df['填写类型'] = res_fill_type
df['退款金额'] = res_refund_money
df['退款原因'] = res_refund_reason
df['申请退款方名称'] = res_refund_name
df['申请退款方账户名'] = res_refund_account
df['开户行'] = res_bank
df['银行账号'] = res_bank_no
df['渠道名称'] = res_channel
df['渠道编码'] = res_channel_no
df['审核意见\n（同意/不同意）'] = res_audit
df['签名数目'] = res_sign_num
df['日期数目'] = res_date_num
df['公章数目'] = res_stamp_num
df['手印数目'] = res_finger_num
df['处理时间'] = res_time
df.to_excel('保证金退还1.xlsx', index=False)

# 