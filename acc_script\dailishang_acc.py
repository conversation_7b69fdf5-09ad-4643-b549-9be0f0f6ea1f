# -*- coding: utf-8 -*-
# @Time    : 2024/12/XX XX:XX
# <AUTHOR> Chen
# @File    : dailishang_acc.py
# @Software: PyCharm
# @Desc    : 代理商申请表准确率验证代码

import os
import json
import base64
import cv2
import numpy as np
import requests
import time
from datetime import datetime
import traceback

class DaiLiShangAccuracy:
    def __init__(self):
        self.dataset_dir = r"D:\myproj\工作交接\译图测试\准确率测试\样本\集团代理商退出申请表"
        self.annotation_file = "acc_img/标注-集团代理商申请表.json"
        self.api_url = "http://127.0.0.1:8083/OcrWeb/azApi/businessAgentRecd"
        self.output_file = "acc_img/dailishang_results.json"

        # 加载标注数据
        self.load_annotations()

    def load_annotations(self):
        """加载标注数据"""
        try:
            with open(self.annotation_file, 'r', encoding='utf-8') as f:
                self.annotations = json.load(f)
            print(f"成功加载标注数据，共 {len(self.annotations)} 个样本")
        except Exception as e:
            print(f"加载标注数据失败: {e}")
            self.annotations = {}

    def image_to_base64(self, image_path):
        """将图片转换为base64编码"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            base64_str = base64.b64encode(image_data).decode('utf-8')
            return base64_str
        except Exception as e:
            print(f"图片转base64失败 {image_path}: {e}")
            return None

    def call_api(self, image_base64):
        """调用API接口"""
        try:
            payload = {
                "imgBase64": image_base64
            }

            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.post(
                self.api_url,
                data=json.dumps(payload),
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            else:
                print(f"API调用失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            print(f"API调用异常: {e}")
            return None

    def parse_api_response(self, api_response):
        """解析API响应数据，处理列表和字典两种格式"""
        try:
            if not api_response or 'result' not in api_response:
                return {}

            result = api_response['result']
            if not result.get('isSuc', False) or 'res' not in result:
                return {}

            res_data = result['res']

            # 如果是列表格式，转换为字典格式
            if isinstance(res_data, list):
                print(f"    检测到列表格式，转换为字典格式...")
                converted_dict = {}

                for section in res_data:
                    if not isinstance(section, dict):
                        continue

                    key = section.get('key', '')
                    value = section.get('value', {})

                    # 根据key提取字段到字典中
                    if key == "代理商填写":
                        converted_dict.update({
                            'refundName': value.get('refundName', ''),
                            'refundAccount': value.get('refundAccount', ''),
                            'bank': value.get('bank', ''),
                            'account': value.get('account', ''),
                            'refundMoney': value.get('refundMoney', ''),
                            'agent_signature': value.get('signature', 0),
                            'agent_date': value.get('date', 0),
                            'agent_seal': value.get('seal', 0),
                            'agent_handprint': value.get('handprint', 0)
                        })
                    elif key == "移动公司填写":
                        converted_dict.update({
                            'company_signature': value.get('signature', 0),
                            'company_date': value.get('date', 0),
                            'company_seal': value.get('seal', 0),
                            'company_handprint': value.get('handprint', 0)
                        })
                        # 处理comment字段
                        comment = value.get('comment', '')
                        if comment == '同意':
                            converted_dict['company_comment1'] = 1
                        elif comment == '不同意':
                            converted_dict['company_comment2'] = 1

                    elif key == "其他事项确认-回收":
                        converted_dict.update({
                            'other_recycle_signature': value.get('signature', 0),
                            'other_recycle_date': value.get('date', 0),
                            'other_recycle_seal': value.get('seal', 0),
                            'other_recycle_handprint': value.get('handprint', 0)
                        })
                        # 处理comment字段
                        comment = value.get('comment', '')
                        if comment == '是':
                            converted_dict['other_recycle_comment1'] = 1
                        elif comment == '否':
                            converted_dict['other_recycle_comment2'] = 1

                    elif key == "其他事项确认-扣罚":
                        converted_dict.update({
                            'other_deduct_signature': value.get('signature', 0),
                            'other_deduct_date': value.get('date', 0),
                            'other_deduct_seal': value.get('seal', 0),
                            'other_deduct_handprint': value.get('handprint', 0)
                        })
                        # 处理comment字段
                        comment = value.get('comment', '')
                        if comment == '是':
                            converted_dict['other_deduct_comment1'] = 1
                        elif comment == '否':
                            converted_dict['other_deduct_comment2'] = 1

                    elif key == "网格审核":
                        converted_dict.update({
                            'director_signature': value.get('signature', 0),
                            'director_date': value.get('date', 0),
                            'director_seal': value.get('seal', 0),
                            'director_handprint': value.get('handprint', 0)
                        })
                        # 处理comment字段
                        comment = value.get('comment', '')
                        if comment == '同意':
                            converted_dict['director_comment1'] = 1
                        elif comment == '不同意':
                            converted_dict['director_comment2'] = 1

                    elif key == "部门经理审核":
                        converted_dict.update({
                            'manage_signature': value.get('signature', 0),
                            'manage_date': value.get('date', 0),
                            'manage_seal': value.get('seal', 0),
                            'manage_handprint': value.get('handprint', 0)
                        })
                        # 处理comment字段
                        comment = value.get('comment', '')
                        if comment == '同意':
                            converted_dict['manage_comment1'] = 1
                        elif comment == '不同意':
                            converted_dict['manage_comment2'] = 1

                return converted_dict
            else:
                # 如果已经是字典格式，直接返回
                print(f"    检测到字典格式，直接使用...")
                return res_data

        except Exception as e:
            print(f"解析API响应失败: {e}")
            return {}

    def process_all_images(self):
        """处理所有图片并获取API结果"""
        results = {}

        # 获取数据集中的所有图片
        if not os.path.exists(self.dataset_dir):
            print(f"数据集目录不存在: {self.dataset_dir}")
            return results

        image_files = [f for f in os.listdir(self.dataset_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        print(f"找到 {len(image_files)} 个图片文件")

        for i, filename in enumerate(image_files):
            print(f"处理 {i+1}/{len(image_files)}: {filename}")

            # 检查是否在标注数据中
            if filename not in self.annotations:
                print(f"  跳过：{filename} 不在标注数据中")
                continue

            image_path = os.path.join(self.dataset_dir, filename)

            # 转换为base64
            base64_str = self.image_to_base64(image_path)
            if not base64_str:
                continue

            # 调用API
            api_response = self.call_api(base64_str)
            if not api_response:
                print(f"  API调用失败: {filename}")
                continue

            # 解析结果
            parsed_result = self.parse_api_response(api_response)
            results[filename] = {
                "api_response": api_response,
                "parsed_result": parsed_result
            }

            print(f"  成功处理: {filename}")

            # 添加延时避免API过载
            time.sleep(0.1)

        return results

    def save_results(self, results):
        """保存结果到JSON文件"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {self.output_file}")
        except Exception as e:
            print(f"保存结果失败: {e}")

    def calculate_accuracy(self, results):
        """计算准确率 - 直接比较API原始返回值与标注文件"""
        if not results:
            print("没有结果数据")
            return

        field_stats = {}
        total_samples = len(results)

        # 创建字段映射：从标注格式到API原始字段名
        field_mapping = {
            # 代理商填写部分
            "代理商填写-refundName": "refundName",
            "代理商填写-refundAccount": "refundAccount",
            "代理商填写-bank": "bank",
            "代理商填写-account": "account",
            "代理商填写-refundMoney": "refundMoney",
            "代理商填写-signature": "agent_signature",
            "代理商填写-date": "agent_date",
            "代理商填写-seal": "agent_seal",
            "代理商填写-handprint": "agent_handprint",

            # 移动公司填写部分
            "移动公司填写-signature": "company_signature",
            "移动公司填写-date": "company_date",
            "移动公司填写-seal": "company_seal",
            "移动公司填写-handprint": "company_handprint",

            # 其他事项确认-回收部分
            "其他事项确认-回收-signature": "other_recycle_signature",
            "其他事项确认-回收-date": "other_recycle_date",
            "其他事项确认-回收-seal": "other_recycle_seal",
            "其他事项确认-回收-handprint": "other_recycle_handprint",

            # 其他事项确认-扣罚部分
            "其他事项确认-扣罚-signature": "other_deduct_signature",
            "其他事项确认-扣罚-date": "other_deduct_date",
            "其他事项确认-扣罚-seal": "other_deduct_seal",
            "其他事项确认-扣罚-handprint": "other_deduct_handprint",

            # 网格审核部分
            "网格审核-signature": "director_signature",
            "网格审核-date": "director_date",
            "网格审核-seal": "director_seal",
            "网格审核-handprint": "director_handprint",

            # 部门经理审核部分
            "部门经理审核-signature": "manage_signature",
            "部门经理审核-date": "manage_date",
            "部门经理审核-seal": "manage_seal",
            "部门经理审核-handprint": "manage_handprint"
        }

        for filename, result_data in results.items():
            if filename not in self.annotations:
                continue

            annotation = self.annotations[filename]
            api_result = result_data.get('parsed_result', {})

            # 比较每个字段
            for annotation_field, expected_value in annotation.items():
                if annotation_field not in field_stats:
                    field_stats[annotation_field] = {'correct': 0, 'total': 0}

                field_stats[annotation_field]['total'] += 1

                # 获取对应的API字段名
                api_field = field_mapping.get(annotation_field)

                if api_field:
                    predicted_value = api_result.get(api_field, '')
                else:
                    # 处理comment字段，需要特殊逻辑
                    if annotation_field.endswith('-comment'):
                        predicted_value = self._get_comment_value(annotation_field, api_result)
                    else:
                        predicted_value = ''

                # 比较值
                if str(expected_value) == str(predicted_value):
                    field_stats[annotation_field]['correct'] += 1
                else:
                    # 调试信息
                    print(f"  不匹配 {filename} - {annotation_field}: 期望='{expected_value}', 实际='{predicted_value}'")

        # 打印结果
        print("\n" + "="*80)
        print("                    代理商申请表准确率统计")
        print("="*80)

        total_correct = 0
        total_fields = 0

        for field, stats in sorted(field_stats.items()):
            correct = stats['correct']
            total = stats['total']
            accuracy = (correct / total * 100) if total > 0 else 0

            print(f"{field:<40} {correct:>3}/{total:<3} = {accuracy:>6.2f}%")

            total_correct += correct
            total_fields += total

        overall_accuracy = (total_correct / total_fields * 100) if total_fields > 0 else 0

        print("="*80)
        print(f"{'总体准确率':<40} {total_correct:>3}/{total_fields:<3} = {overall_accuracy:>6.2f}%")
        print(f"处理样本数: {total_samples}")
        print("="*80)

        return field_stats, overall_accuracy

    def _get_comment_value(self, annotation_field, api_result):
        """根据API结果推断comment字段的值"""
        if annotation_field == "移动公司填写-comment":
            if 'company_comment2' in api_result:
                return '不同意'
            elif 'company_comment1' in api_result or 'company_comment3' in api_result:
                return '同意'
        elif annotation_field == "其他事项确认-回收-comment":
            if 'other_recycle_comment2' in api_result:
                return '否'
            elif 'other_recycle_comment1' in api_result:
                return '是'
        elif annotation_field == "其他事项确认-扣罚-comment":
            if 'other_deduct_comment2' in api_result:
                return '否'
            elif 'other_deduct_comment1' in api_result:
                return '是'
        elif annotation_field == "网格审核-comment":
            if 'director_comment2' in api_result:
                return '不同意'
            elif 'director_comment1' in api_result or 'director_comment3' in api_result:
                return '同意'
        elif annotation_field == "部门经理审核-comment":
            if 'manage_comment2' in api_result:
                return '不同意'
            elif 'manage_comment1' in api_result or 'manage_comment3' in api_result:
                return '同意'

        return ''

def main():
    """主函数"""
    print("开始代理商申请表准确率验证...")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 创建验证器
    validator = DaiLiShangAccuracy()

    # 处理所有图片
    print("\n1. 处理图片并调用API...")
    results = validator.process_all_images()

    # 保存结果
    print("\n2. 保存结果...")
    validator.save_results(results)

    # 计算准确率
    print("\n3. 计算准确率...")
    validator.calculate_accuracy(results)

    print(f"\n验证完成! 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == '__main__':
    main()
'''
代理商填写-account                             75/93  =  80.65%
代理商填写-bank                                70/93  =  75.27%
代理商填写-date                                86/93  =  92.47%
代理商填写-handprint                           91/93  =  97.85%
代理商填写-refundAccount                       81/93  =  87.10%
代理商填写-refundMoney                         75/93  =  80.65%
代理商填写-refundName                          77/93  =  82.80%
代理商填写-seal                                93/93  = 100.00%
代理商填写-signature                           91/93  =  97.85%
其他事项确认-回收-comment                         78/93  =  83.87%
其他事项确认-回收-date                            91/93  =  97.85%
其他事项确认-回收-handprint                       93/93  = 100.00%
其他事项确认-回收-seal                            93/93  = 100.00%
其他事项确认-回收-signature                       91/93  =  97.85%
其他事项确认-扣罚-comment                         88/93  =  94.62%
其他事项确认-扣罚-date                            92/93  =  98.92%
其他事项确认-扣罚-handprint                       93/93  = 100.00%
其他事项确认-扣罚-seal                            91/93  =  97.85%
其他事项确认-扣罚-signature                       89/93  =  95.70%
移动公司填写-comment                            93/93  = 100.00%
移动公司填写-date                               92/93  =  98.92%
移动公司填写-handprint                          92/93  =  98.92%
移动公司填写-seal                               93/93  = 100.00%
移动公司填写-signature                          90/93  =  96.77%
网格审核-comment                              91/93  =  97.85%
网格审核-date                                 93/93  = 100.00%
网格审核-handprint                            93/93  = 100.00%
网格审核-seal                                 88/93  =  94.62%
网格审核-signature                            91/93  =  97.85%
部门经理审核-comment                            91/93  =  97.85%
部门经理审核-date                               93/93  = 100.00%
部门经理审核-handprint                          93/93  = 100.00%
部门经理审核-seal                               93/93  = 100.00%
部门经理审核-signature                          93/93  = 100.00%
================================================================================
总体准确率                                    3017/3162 =  95.41%
'''