import os
import requests
import base64
import traceback

ocr_url = os.environ.get('OCR_URL', 'http://192.168.1.138:31058/anyocr')



def file2base64(file_path):
    if os.path.exists(file_path):
        with open(file_path, 'rb') as f:
            base64_data = base64.b64encode(f.read())
            return base64_data.decode()
    else:
        return ''

class OcrEngine():
    def __init__(self):
        pass
    
    def ocr_base(self, imgbase64):
        try:
            res = requests.post(url=ocr_url, json={"image": imgbase64,"det":False},headers={"content-type": "application/json"})
            return res.json()['data']
        except Exception as e:
            traceback.print_exc()
            print(e)
            return ""

    def ocr_with_det(self, imgbase64):
        '''
        {'data': [[[[11.0, 75.0], [122.0, 75.0], [122.0, 105.0], [11.0, 105.0]], '渠道编码'], [[[10.0, 9.0], [122.0, 9.0], [122.0, 39.0], [10.0, 39.0]], '渠道名称']], 'resp_msg': 'OK', 'resp_code': 200}
        '''
        try:
            res = requests.post(url=ocr_url, json={"image": imgbase64,"det":True},headers={"content-type": "application/json"})
            data = res.json()['data']
            if 'NoneType' in data:
                res = self.ocr_base(imgbase64)
                return res
            else:
                tmp_data = []
                for item in data:
                    max_y = item[0][0][1]
                    ocr_res = item[1]
                    tmp_data.append([max_y, ocr_res])
                tmp_data = sorted(tmp_data, key=lambda x: x[0])
                result = ''
                for item in tmp_data:
                    result += item[1]
                return result
        except Exception as e:
            traceback.print_exc()
            print(e)
            return ""


if __name__ == '__main__':
    ocr_engine = OcrEngine()
    data = file2base64(r"D:\myproj\2025\caiwuyu\tmp\settlementType_9.jpg")
    res = ocr_engine.ocr_with_det(data)
    print(res)