from ultralytics import YOLO
import torch
import os
import cv2
import time
import numpy as np
import uuid

YOLO_DEVICE = os.environ.get('YOLO_DEVICE', 'cpu')
YOLO_CONF = os.environ.get('YOLO_CONF', '0.5')
YOLO_CONF = float(YOLO_CONF)
YOLO_IOU = os.environ.get('YOLO_IOU', '0.35')
YOLO_IOU = float(YOLO_IOU)
YOLO_WEIGHT_PATH = os.environ.get('YOLO_WEIGHT_PATH', './weights/baozhengjin0519.pt')

class YoloEngine:
    def __init__(self, yolo_weight_path=YOLO_WEIGHT_PATH, conf=YOLO_CONF, iou=YOLO_IOU, label=[], device='cuda'):
        t0 = time.time()
        self.device = device
        self.conf = conf
        self.iou = iou
        self.yolo_engine = YOLO(yolo_weight_path)
        # ERP单号、采购订单编号、合同编号、项目编号、印章、标题
        self.label = label
        print(f'\nyolo模型加载成功，耗时：{time.time()-t0}s\nyolo weight_path:{yolo_weight_path}、conf:{conf}、iou:{iou}、device:{device}')

    def infer_base(self, image):
        '''
        接受np/cv格式的图片数据，返回{类别：[截图]}字典
        '''
        res_dict = {}
        if self.device != 'cpu' and torch.cuda.is_available():
            yolo_res = self.yolo_engine.predict(image, conf=self.conf, iou=self.iou, device='cuda')[0]
        else:
            yolo_res = self.yolo_engine.predict(image, conf=self.conf, iou=self.iou, device='cpu')[0]
        for r in yolo_res:
            # 获取目标检测结果
            boxes = r.boxes
            cls = boxes.cls
            conf = boxes.conf
            xyxy = boxes.xyxy
            xywh = boxes.xywh
            for i in range(len(cls)):
                bbox = xyxy[i].tolist()
                cropped_cls = cls[i]
                cropped_name = self.label[int(cropped_cls)]
                cropped_image = image[int(bbox[1]):int(bbox[3]), int(bbox[0])-10:int(bbox[2])+10]

                center = (int(bbox[1]) + int(bbox[3]))/2

                if cropped_name not in res_dict.keys():
                    res_dict[cropped_name] = [(cropped_image, center)]
                else:
                    res_dict[cropped_name].append((cropped_image, center))

        # 将res_dict的值根据center从小到大排序，并且去除调centerv值
        for k, v in res_dict.items():
            sorted_data = sorted(v, key=lambda x: x[1])
            res_dict[k] = [item[0] for item in sorted_data]

        return res_dict

    def infer_one(self, image, one_list):
        '''
        针对输入类别，只保留最大置信度的截图。其余类别与infer_base保持一致
        '''
        res_dict = {}
        score_dict = {}  # 用于记录one_list中类别的最大置信度
        if self.device != 'cpu' and torch.cuda.is_available():
            yolo_res = self.yolo_engine.predict(image, conf=self.conf, iou=self.iou, device='cuda')[0]
        else:
            yolo_res = self.yolo_engine.predict(image, conf=self.conf, iou=self.iou, device='cpu')[0]
        for r in yolo_res:
            # 获取目标检测结果
            boxes = r.boxes
            cls = boxes.cls
            conf = boxes.conf
            xyxy = boxes.xyxy
            xywh = boxes.xywh
            for i in range(len(cls)):
                bbox = xyxy[i].tolist()
                cropped_cls = cls[i]
                cropped_name = self.label[int(cropped_cls)]
                cropped_image = image[int(bbox[1]):int(bbox[3]), int(bbox[0])-10:int(bbox[2])+10]

                center = (int(bbox[1]) + int(bbox[3]))/2

                if cropped_name in one_list:
                    # 对于one_list中的类别，只保留最大置信度的截图
                    if cropped_name in score_dict.keys():
                        if conf[i] > score_dict[cropped_name]:
                            res_dict[cropped_name] = [cropped_image]
                            score_dict[cropped_name] = conf[i]
                    else:
                        score_dict[cropped_name] = conf[i]
                        res_dict[cropped_name] = [cropped_image]
                else:
                    # 对于其他类别，与infer_base保持一致，保留所有检测结果
                    if cropped_name not in res_dict.keys():
                        res_dict[cropped_name] = [(cropped_image, center)]
                    else:
                        res_dict[cropped_name].append((cropped_image, center))

        # 对于非one_list中的类别，根据center从小到大排序，并去除center值
        for k, v in res_dict.items():
            if k not in one_list and isinstance(v[0], tuple):
                sorted_data = sorted(v, key=lambda x: x[1])
                res_dict[k] = [item[0] for item in sorted_data]

        return res_dict


if __name__ == '__main__':
    label = ['outlet_signature','outlet_date','outlet_seal','outlet_handprint','refundMoney','refundReason1','refundReason2','refundReason3','refundName','refundAccount','bank','account','company_signature','company_date','company_seal','company_handprint','company_comment1','company_comment2','channelName','channelCode','director_signature','director_date','director_seal','director_handprint','director_comment1','director_comment2','manage_signature','manage_date','manage_seal','manage_handprint','manage_comment1','manage_comment2']
    yolo = YoloEngine(label=label)
    img = cv2.imdecode(np.fromfile(r"C:\Users\<USER>\Desktop\财务域能力开发-cbl\保证金退还审批单\baozhengjin\302123R04231114001-0005.jpg", dtype=np.uint8), cv2.IMREAD_COLOR)

    res = yolo.infer_base(img)
    for k, v in res.items():
        for item in v:
            cv2.imencode('.jpg', item)[1].tofile('./tmp/'+k+'+++'+str(uuid.uuid4())+'.jpg')